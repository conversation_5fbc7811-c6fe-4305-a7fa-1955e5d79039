# ProxyRedirect Dylib - Complete Implementation Summary

## 🎯 **What Changed**

The dylib has been completely rewritten to redirect **ALL HTTP/HTTPS requests** to a proxy server instead of just Binance domains.

### **New Redirection Format:**
- **Before**: `https://api.binance.com/v3/ticker` → `https://api.binance.com.x3r0x.me/v3/ticker`
- **After**: `https://example.com/path/file.php` → `https://proxy.x3r0x.me/example.com/path/file.php`

## 🔧 **Key Features**

### ✅ **Universal Redirection**
- Redirects ALL domains (not just Binance)
- Format: `https://proxy.x3r0x.me/[original-domain]/[path]`
- Preserves query parameters and fragments

### ✅ **Smart Exclusions**
Automatically excludes system/Apple domains:
- `apple.com`, `icloud.com`, `itunes.apple.com`
- `localhost`, `127.0.0.1`, `::1`
- Apple DNS and push services
- Already proxied URLs (prevents double redirection)

### ✅ **Comprehensive API Coverage**
Hooks all major iOS networking methods:
- `NSURL URLWithString:`
- `NSURLRequest requestWithURL:` & `initWithURL:`
- `NSMutableURLRequest requestWithURL:`
- `NSURLSession dataTaskWithURL:` & `dataTaskWithRequest:`
- `NSURLSession downloadTask` & `uploadTask` methods

### ✅ **Enhanced Logging**
- Initialization messages: `[ProxyRedirect] Dylib v1.0.1 loaded`
- Hook installation: `[ProxyRedirect] Successfully swizzled + URLWithString:`
- Redirection tracking: `[ProxyRedirect] NSURL redirected: https://example.com → https://proxy.x3r0x.me/example.com`
- Verbose mode for debugging all hook calls

## 📊 **Test Results**

### **Redirection Logic Test:**
```
✅ PASS: https://example.com/path/file.php → https://proxy.x3r0x.me/example.com/path/file.php
✅ PASS: http://api.binance.com/v3/ticker → https://proxy.x3r0x.me/api.binance.com/v3/ticker
✅ PASS: https://www.google.com/search?q=test → https://proxy.x3r0x.me/www.google.com/search?q=test
✅ PASS: https://apple.com/iphone → https://apple.com/iphone (excluded)
✅ PASS: https://localhost:8080/api → https://localhost:8080/api (excluded)
```

### **Build Results:**
- **Device**: `libBinanceRedirect.dylib` (56KB, ARM64)
- **Simulator**: `libBinanceRedirect_sim.dylib` (20KB, x86_64)
- **Universal**: `libBinanceRedirect_universal.dylib` (87KB, both architectures)

## 🚀 **Usage Instructions**

### **1. Build the Dylib**
```bash
make clean
make device  # For iOS device
```

### **2. Inject into IPA**
```bash
# Using the helper script
python inject_helper.py --ipa YourApp.ipa --dylib libBinanceRedirect.dylib --output YourApp_injected.ipa

# Or manually with insert_dylib
insert_dylib @executable_path/libBinanceRedirect.dylib YourAppExecutable
```

### **3. Expected Console Output**
When the dylib loads successfully:
```
[ProxyRedirect] Dylib v1.0.1 loaded - initializing URL redirection hooks
[ProxyRedirect] Successfully swizzled + URLWithString:
[ProxyRedirect] Successfully swizzled + requestWithURL:
[ProxyRedirect] Successfully swizzled - initWithURL:
[ProxyRedirect] All hooks installed successfully
```

When URLs are redirected:
```
[ProxyRedirect] NSURL redirected: https://api.example.com/data → https://proxy.x3r0x.me/api.example.com/data
[ProxyRedirect] NSURLSession dataTaskWithURL redirected: https://cdn.example.com/image.jpg → https://proxy.x3r0x.me/cdn.example.com/image.jpg
```

### **4. Network Traffic Verification**
Use Fiddler/Charles Proxy to verify that requests are going to:
- `https://proxy.x3r0x.me/[original-domain]/[path]`
- NOT to the original domains (except excluded ones)

## ⚙️ **Configuration**

### **Proxy Server** (config.h):
```c
#define PROXY_SERVER "https://proxy.x3r0x.me"
```

### **Add Excluded Domains**:
```c
static const char* EXCLUDED_DOMAINS[] = {
    "localhost",
    "apple.com",
    "your-domain.com",  // Add your domains here
    // ...
};
```

### **Debug Settings**:
```c
#define BINANCE_REDIRECT_LOGGING 1  // Enable logging
#define VERBOSE_LOGGING 1           // Log all hook calls
#define LOG_SENSITIVE_DATA 1        // Log full URLs
```

## 🔍 **Testing the Dylib**

### **Test Hook Functionality:**
```bash
clang -framework Foundation -o test_proxy_hooks test_proxy_hooks.m
./test_proxy_hooks
```

### **Test Redirection Logic:**
```bash
clang -framework Foundation -o test_proxy_redirect test_proxy_redirect.m
./test_proxy_redirect
```

## 🎯 **Expected Behavior**

### **✅ Should Redirect:**
- `https://example.com/api` → `https://proxy.x3r0x.me/example.com/api`
- `https://api.binance.com/v3` → `https://proxy.x3r0x.me/api.binance.com/v3`
- `https://www.google.com/search` → `https://proxy.x3r0x.me/www.google.com/search`
- Any HTTP/HTTPS request to non-excluded domains

### **❌ Should NOT Redirect:**
- `https://apple.com/*` (Apple services)
- `https://localhost/*` (Local development)
- `https://proxy.x3r0x.me/*` (Already proxied)
- System/push notification services

## 🚨 **Troubleshooting**

If the dylib still doesn't work:

1. **Check console logs** for `[ProxyRedirect]` messages
2. **Verify injection** with `otool -L /path/to/app/executable`
3. **Monitor network traffic** with proxy tools
4. **Enable verbose logging** in config.h
5. **Check excluded domains** - your target might be excluded

The dylib now intercepts ALL network requests and should work with any iOS app that uses standard networking APIs.

## 📁 **Files Updated**
- `config.h` - New proxy configuration
- `BinanceRedirect.mm` - Complete rewrite for proxy redirection
- `README.md` - Updated documentation
- `test_proxy_redirect.m` - New test for proxy logic
- `test_proxy_hooks.m` - New test for hook functionality

The dylib is now a universal proxy redirector that will route all app traffic through your proxy server!
