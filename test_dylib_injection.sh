#!/bin/bash

# Test script to verify dylib injection works locally
# This creates a test app and injects our dylib to verify it works

echo "🧪 Testing BinanceRedirect Dylib Injection"
echo "=========================================="

# Build the test program
echo "📱 Building test program..."
clang -framework Foundation -o test_hooks test_hooks.m
if [ $? -ne 0 ]; then
    echo "❌ Failed to build test program"
    exit 1
fi

echo "✅ Test program built successfully"

# Run the test program without dylib first
echo ""
echo "🔍 Running test program WITHOUT dylib injection:"
echo "------------------------------------------------"
./test_hooks

echo ""
echo "🔍 Running test program WITH dylib injection:"
echo "---------------------------------------------"

# Set environment variable to load our dylib (use simulator version for macOS testing)
export DYLD_INSERT_LIBRARIES="./libBinanceRedirect_sim.dylib"

# Run the test program with dylib injection
./test_hooks

# Clean up
unset DYLD_INSERT_LIBRARIES

echo ""
echo "🎯 Injection test completed!"
echo ""
echo "📋 What to look for:"
echo "   - WITHOUT dylib: URLs should remain unchanged"
echo "   - WITH dylib: Binance URLs should be redirected to .x3r0x.me domains"
echo "   - You should see [BinanceRedirect] log messages when dylib is loaded"
echo ""
echo "If you see redirected URLs and log messages, the dylib is working correctly!"
