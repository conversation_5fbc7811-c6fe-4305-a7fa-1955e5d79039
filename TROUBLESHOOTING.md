# BinanceRedirect Dylib - Troubleshooting Guide

## 🔧 Recent Fixes Applied

### Critical Issues Fixed:
1. **Proper Method Swizzling**: Replaced incorrect `method_setImplementation` with proper swizzling
2. **Comprehensive API Coverage**: Added hooks for more networking methods:
   - `NSURL URLWithString:` (class method)
   - `NSURLRequest requestWithURL:` (class method)  
   - `NSURLRequest initWithURL:` (instance method)
   - `NSMutableURLRequest requestWithURL:` (class method)
   - `NSURLSession dataTaskWithURL:completionHandler:`
   - `NSURLSession dataTaskWithRequest:completionHandler:`
   - `NSURLSession downloadTaskWithURL:completionHandler:`
   - `NSURLSession downloadTaskWithRequest:completionHandler:`
   - `NSURLSession uploadTaskWithRequest:fromData:completionHandler:`

3. **Enhanced Logging**: Added verbose logging to track all hook calls
4. **Debug Mode Enabled**: Temporarily enabled debug logging to help troubleshoot
5. **Double Redirection Fix**: Prevented URLs from being redirected multiple times

## 🚨 If Dylib Still Doesn't Work

### Step 1: Verify Dylib is Loaded
Check device console logs for these messages:
```
[BinanceRedirect] Dylib v1.0.0 loaded - initializing URL redirection hooks
[BinanceRedirect] Successfully swizzled + URLWithString:
[BinanceRedirect] Successfully swizzled + requestWithURL:
[BinanceRedirect] Successfully swizzled - initWithURL:
[BinanceRedirect] All hooks installed successfully
```

**If you don't see these messages:**
- Dylib is not being loaded
- Check IPA injection was successful: `otool -L /path/to/app/executable`
- Verify dylib exists in app bundle
- Check code signing

### Step 2: Verify Hooks are Being Called
With verbose logging enabled, you should see:
```
[BinanceRedirect] NSURL URLWithString called with: https://api.binance.com/...
[BinanceRedirect] NSURLRequest requestWithURL called with: https://native.binance.com/...
[BinanceRedirect] NSURLSession dataTaskWithURL called with: https://stream.binance.com/...
```

**If you don't see these messages:**
- App is using different networking APIs
- Try enabling additional hooks in config.h
- App might be using lower-level networking (CFNetwork, etc.)

### Step 3: Verify Redirection Logic
You should see redirection messages:
```
[BinanceRedirect] NSURL redirected: https://api.binance.com/... -> https://api.binance.com.x3r0x.me/...
[BinanceRedirect] NSURLSession dataTaskWithURL redirected: https://native.binance.com/... -> https://native.binance.com.x3r0x.me/...
```

**If hooks are called but no redirection:**
- Check if URLs actually contain ".binance."
- Verify domain mappings in config.h
- Test redirection logic with: `./test_redirect`

## 🔍 Advanced Debugging

### Enable Maximum Logging
Edit `config.h`:
```c
#define BINANCE_REDIRECT_LOGGING 1
#define LOG_SENSITIVE_DATA 1
#define DEBUG_MODE 1
#define VERBOSE_LOGGING 1
```

### Check for Additional Networking APIs
Some apps use:
- `CFURLRequest` / `CFURLConnection`
- `NSURLConnection` (deprecated but still used)
- Third-party networking libraries (Alamofire, AFNetworking)
- Custom networking implementations

### Hook Additional Methods
Enable in config.h:
```c
#define HOOK_CFURL_CREATION 1
#define HOOK_NSURLCONNECTION 1
#define HOOK_ALL_URL_METHODS 1
```

## 🛠️ Common Issues & Solutions

### Issue: App Crashes on Launch
**Cause**: Incompatible dylib or signing issues
**Solution**: 
- Use correct architecture (ARM64 for device, x86_64 for simulator)
- Re-sign app after injection
- Check for conflicting dylibs

### Issue: No Network Requests Intercepted
**Cause**: App uses different networking stack
**Solution**:
- Monitor with network proxy (Charles, mitmproxy) to see actual requests
- Hook additional networking APIs
- Check if app uses custom networking libraries

### Issue: URLs Not Redirected
**Cause**: Redirection logic not matching URLs
**Solution**:
- Check exact URL format in logs
- Verify domain mappings in config.h
- Test with: `./test_redirect`

### Issue: Partial Redirection
**Cause**: Some networking paths not hooked
**Solution**:
- Enable all hooks in config.h
- Add custom hooks for specific APIs
- Monitor all network traffic to identify missed calls

## 📱 Testing Recommendations

### 1. Use Network Monitoring
- **Charles Proxy**: Set up HTTP/HTTPS proxy on device
- **Wireshark**: Monitor network packets
- **mitmproxy**: Command-line network monitoring

### 2. Console Monitoring
- Xcode → Window → Devices and Simulators → Console
- Filter for "[BinanceRedirect]" messages
- Look for both hook calls and redirection messages

### 3. Incremental Testing
1. Test dylib loading (initialization messages)
2. Test hook installation (swizzling messages)  
3. Test hook calls (verbose logging)
4. Test redirection logic (redirection messages)
5. Verify network traffic (proxy monitoring)

## 🔧 Build Verification

Ensure your build is correct:
```bash
# Check architecture
lipo -info libBinanceRedirect.dylib

# Check dependencies  
otool -L libBinanceRedirect.dylib

# Test redirection logic
./test_redirect

# Verify all variants built
ls -la *.dylib
```

## 📞 Still Not Working?

If the dylib still doesn't work after following this guide:

1. **Capture detailed logs** with all debugging enabled
2. **Monitor network traffic** with a proxy tool
3. **Check app's networking implementation** - it might use custom APIs
4. **Consider the app's architecture** - some apps have complex networking layers

The dylib hooks the most common iOS networking APIs, but some apps may use:
- Custom networking libraries
- Lower-level C APIs (CFNetwork)
- Third-party frameworks
- WebView-based networking

In such cases, additional custom hooks may be needed for the specific app.
