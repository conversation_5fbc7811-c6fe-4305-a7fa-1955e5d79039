#ifndef BINANCE_REDIRECT_CONFIG_H
#define BINANCE_REDIRECT_CONFIG_H

// ProxyRedirect Configuration
// ===========================
//
// This file contains configuration options for the ProxyRedirect dylib.
// Redirects ALL HTTP/HTTPS requests to a proxy server.

// Enable/disable logging
#define BINANCE_REDIRECT_LOGGING 1

// Log prefix for console output
#define LOG_PREFIX "[ProxyRedirect]"

// Proxy server configuration
#define PROXY_SERVER "https://proxy.x3r0x.me"

// Enable redirection for all domains (not just specific ones)
#define REDIRECT_ALL_REQUESTS 1

// Domains to exclude from redirection (local/system domains)
static const char* EXCLUDED_DOMAINS[] = {
    "localhost",
    "127.0.0.1",
    "0.0.0.0",
    "::1",
    "apple.com",
    "icloud.com",
    "itunes.apple.com",
    "apps.apple.com",
    "mzstatic.com",
    "apple-dns.net",
    "push.apple.com",
    "captive.apple.com",
    "gsp-ssl.ls.apple.com",
    "gs-loc.apple.com",
    // Add more excluded domains as needed
};

// Number of excluded domains
#define EXCLUDED_DOMAINS_COUNT (sizeof(EXCLUDED_DOMAINS) / sizeof(EXCLUDED_DOMAINS[0]))

// Legacy settings (kept for compatibility)
#define REDIRECT_SUFFIX ".x3r0x.me"
#define ENABLE_WILDCARD_REDIRECT 0
#define BINANCE_DOMAIN_PATTERN @""
#define BINANCE_REPLACEMENT_TEMPLATE @""

// Dummy domain mappings (not used in proxy mode)
static const struct {
    const char* original;
    const char* replacement;
} DOMAIN_MAPPINGS[] = {
    {"dummy", "dummy"}
};
#define DOMAIN_MAPPINGS_COUNT 0

// Hook configuration - enable/disable specific API hooks
#define HOOK_NSURL_CREATION 1
#define HOOK_NSURLREQUEST_INIT 1
#define HOOK_NSURLSESSION_DATATASK 1

// Additional networking APIs to hook (experimental)
#define HOOK_CFURL_CREATION 0  // Core Foundation URL creation
#define HOOK_NSURLCONNECTION 0  // Legacy NSURLConnection (deprecated but still used)

// Performance settings
#define CACHE_REDIRECTED_URLS 1  // Cache URL redirections to improve performance
#define MAX_CACHE_SIZE 1000      // Maximum number of URLs to cache

// Security settings
#define VERIFY_HTTPS_REDIRECT 1  // Ensure HTTPS URLs remain HTTPS after redirect
#define LOG_SENSITIVE_DATA 1     // Log full URLs (may contain sensitive data) - ENABLED FOR DEBUGGING

// Debug settings (ENABLED for troubleshooting)
#define DEBUG_MODE 1
#define VERBOSE_LOGGING 1
#define HOOK_ALL_URL_METHODS 1  // Hook additional URL methods for debugging

// Version information
#define BINANCE_REDIRECT_VERSION "1.0.1"
#define BINANCE_REDIRECT_BUILD_DATE __DATE__ " " __TIME__

#endif // BINANCE_REDIRECT_CONFIG_H
