#ifndef BINANCE_REDIRECT_CONFIG_H
#define BINANCE_REDIRECT_CONFIG_H

// BinanceRedirect Configuration
// ============================
// 
// This file contains configuration options for the BinanceRedirect dylib.
// Modify these values to customize the redirection behavior.

// Enable/disable logging
#define BINANCE_REDIRECT_LOGGING 1

// Log prefix for console output
#define LOG_PREFIX "[BinanceRedirect]"

// Target domain suffix to append to Binance domains
#define REDIRECT_SUFFIX ".x3r0x.me"

// Specific domain mappings
// These are the exact domain replacements that will be performed
static const struct {
    const char* original;
    const char* replacement;
} DOMAIN_MAPPINGS[] = {
    {"native.binance.com", "native.binance.com.x3r0x.me"},
    {"native.binance.info", "native.binance.info.x3r0x.me"},
    {"api.binance.com", "api.binance.com.x3r0x.me"},
    {"www.binance.com", "www.binance.com.x3r0x.me"},
    {"binance.com", "binance.com.x3r0x.me"},
    // Add more specific mappings here as needed
};

// Number of domain mappings
#define DOMAIN_MAPPINGS_COUNT (sizeof(DOMAIN_MAPPINGS) / sizeof(DOMAIN_MAPPINGS[0]))

// Enable generic wildcard redirection for any *.binance.* domain
// This will catch any binance subdomain not explicitly listed above
#define ENABLE_WILDCARD_REDIRECT 1

// Regex pattern for wildcard matching (if ENABLE_WILDCARD_REDIRECT is enabled)
#define BINANCE_DOMAIN_PATTERN @"([a-zA-Z0-9-]+\\.binance\\.[a-zA-Z]+)"
#define BINANCE_REPLACEMENT_TEMPLATE @"$1.x3r0x.me"

// Hook configuration - enable/disable specific API hooks
#define HOOK_NSURL_CREATION 1
#define HOOK_NSURLREQUEST_INIT 1
#define HOOK_NSURLSESSION_DATATASK 1

// Additional networking APIs to hook (experimental)
#define HOOK_CFURL_CREATION 0  // Core Foundation URL creation
#define HOOK_NSURLCONNECTION 0  // Legacy NSURLConnection (deprecated but still used)

// Performance settings
#define CACHE_REDIRECTED_URLS 1  // Cache URL redirections to improve performance
#define MAX_CACHE_SIZE 1000      // Maximum number of URLs to cache

// Security settings
#define VERIFY_HTTPS_REDIRECT 1  // Ensure HTTPS URLs remain HTTPS after redirect
#define LOG_SENSITIVE_DATA 0     // Log full URLs (may contain sensitive data)

// Debug settings (only enable for development)
#define DEBUG_MODE 0
#define VERBOSE_LOGGING 0
#define HOOK_ALL_URL_METHODS 0  // Hook additional URL methods for debugging

// Version information
#define BINANCE_REDIRECT_VERSION "1.0.0"
#define BINANCE_REDIRECT_BUILD_DATE __DATE__ " " __TIME__

#endif // BINANCE_REDIRECT_CONFIG_H
