#import <Foundation/Foundation.h>
#import <objc/runtime.h>
#import <dlfcn.h>
#import "config.h"

// Function prototypes for method swizzling
static IMP original_initWithURL = NULL;
static IMP original_initWithRequest = NULL;
static IMP original_dataTaskWithURL = NULL;
static IMP original_dataTaskWithRequest = NULL;

// URL cache for performance optimization
#if CACHE_REDIRECTED_URLS
static NSMutableDictionary* urlCache = nil;
static dispatch_queue_t cacheQueue = nil;
#endif

// Helper function to redirect Binance URLs
NSString* redirectBinanceURL(NSString* originalURL) {
    if (!originalURL) return originalURL;

#if CACHE_REDIRECTED_URLS
    // Check cache first
    if (urlCache) {
        __block NSString* cached = nil;
        dispatch_sync(cacheQueue, ^{
            cached = [urlCache objectForKey:originalURL];
        });
        if (cached) {
            return cached;
        }
    }
#endif

    NSString* urlString = [originalURL copy];
    BOOL wasRedirected = NO;

    // Check if URL contains binance domain
    if ([urlString containsString:@".binance."]) {
        // Apply specific domain mappings first
        for (int i = 0; i < DOMAIN_MAPPINGS_COUNT; i++) {
            NSString* original = [NSString stringWithUTF8String:DOMAIN_MAPPINGS[i].original];
            NSString* replacement = [NSString stringWithUTF8String:DOMAIN_MAPPINGS[i].replacement];

            if ([urlString containsString:original]) {
                urlString = [urlString stringByReplacingOccurrencesOfString:original
                                                                withString:replacement];
                wasRedirected = YES;
            }
        }

#if ENABLE_WILDCARD_REDIRECT
        // Handle other binance subdomains with wildcard pattern
        if (!wasRedirected) {
            NSRegularExpression* regex = [NSRegularExpression regularExpressionWithPattern:BINANCE_DOMAIN_PATTERN
                                                                                   options:0
                                                                                     error:nil];
            if (regex) {
                NSString* newString = [regex stringByReplacingMatchesInString:urlString
                                                                      options:0
                                                                        range:NSMakeRange(0, [urlString length])
                                                                 withTemplate:BINANCE_REPLACEMENT_TEMPLATE];
                if (![newString isEqualToString:urlString]) {
                    urlString = newString;
                    wasRedirected = YES;
                }
            }
        }
#endif

#if VERIFY_HTTPS_REDIRECT
        // Ensure HTTPS URLs remain HTTPS
        if (wasRedirected && [originalURL hasPrefix:@"https://"] && ![urlString hasPrefix:@"https://"]) {
            urlString = [urlString stringByReplacingOccurrencesOfString:@"http://" withString:@"https://"];
        }
#endif

#if BINANCE_REDIRECT_LOGGING
        if (wasRedirected) {
#if LOG_SENSITIVE_DATA
            NSLog(@"%@ Redirected URL: %@ -> %@", @LOG_PREFIX, originalURL, urlString);
#else
            NSLog(@"%@ URL redirected (domain: %@)", @LOG_PREFIX, [[NSURL URLWithString:originalURL] host]);
#endif
        }
#endif
    }

#if CACHE_REDIRECTED_URLS
    // Cache the result
    if (urlCache && wasRedirected) {
        dispatch_async(cacheQueue, ^{
            if ([urlCache count] >= MAX_CACHE_SIZE) {
                [urlCache removeAllObjects];  // Simple cache eviction
            }
            [urlCache setObject:urlString forKey:originalURL];
        });
    }
#endif

    return urlString;
}

// NSURL hook
NSURL* hooked_initWithString(id self, SEL _cmd, NSString* URLString) {
    NSString* redirectedURL = redirectBinanceURL(URLString);
    return ((NSURL* (*)(id, SEL, NSString*))original_initWithURL)(self, _cmd, redirectedURL);
}

// NSURLRequest hook
NSURLRequest* hooked_initWithURL(id self, SEL _cmd, NSURL* URL) {
    NSString* redirectedURLString = redirectBinanceURL([URL absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    return ((NSURLRequest* (*)(id, SEL, NSURL*))original_initWithRequest)(self, _cmd, redirectedURL);
}

// NSURLSession dataTask hooks
NSURLSessionDataTask* hooked_dataTaskWithURL(id self, SEL _cmd, NSURL* url, void (^completionHandler)(NSData*, NSURLResponse*, NSError*)) {
    NSString* redirectedURLString = redirectBinanceURL([url absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    return ((NSURLSessionDataTask* (*)(id, SEL, NSURL*, void (^)(NSData*, NSURLResponse*, NSError*)))original_dataTaskWithURL)(self, _cmd, redirectedURL, completionHandler);
}

NSURLSessionDataTask* hooked_dataTaskWithRequest(id self, SEL _cmd, NSURLRequest* request, void (^completionHandler)(NSData*, NSURLResponse*, NSError*)) {
    NSString* redirectedURLString = redirectBinanceURL([[request URL] absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    NSMutableURLRequest* mutableRequest = [request mutableCopy];
    [mutableRequest setURL:redirectedURL];
    
    return ((NSURLSessionDataTask* (*)(id, SEL, NSURLRequest*, void (^)(NSData*, NSURLResponse*, NSError*)))original_dataTaskWithRequest)(self, _cmd, mutableRequest, completionHandler);
}

// Method swizzling helper
void swizzleMethod(Class targetClass, SEL originalSelector, SEL swizzledSelector, IMP* originalIMP) {
    Method originalMethod = class_getInstanceMethod(targetClass, originalSelector);
    Method swizzledMethod = class_getInstanceMethod(targetClass, swizzledSelector);

    if (originalMethod && swizzledMethod) {
        *originalIMP = method_getImplementation(originalMethod);
        method_setImplementation(originalMethod, method_getImplementation(swizzledMethod));
        NSLog(@"[BinanceRedirect] Successfully swizzled %@", NSStringFromSelector(originalSelector));
    } else {
        NSLog(@"[BinanceRedirect] Failed to swizzle %@", NSStringFromSelector(originalSelector));
    }
}

// Constructor - called when dylib is loaded
__attribute__((constructor))
static void initialize() {
#if BINANCE_REDIRECT_LOGGING
    NSLog(@"%@ Dylib v%@ loaded - initializing URL redirection hooks", @LOG_PREFIX, @BINANCE_REDIRECT_VERSION);
    NSLog(@"%@ Build: %s", @LOG_PREFIX, BINANCE_REDIRECT_BUILD_DATE);
#endif

#if CACHE_REDIRECTED_URLS
    // Initialize URL cache
    urlCache = [[NSMutableDictionary alloc] init];
    cacheQueue = dispatch_queue_create("com.binanceredirect.cache", DISPATCH_QUEUE_SERIAL);
#endif

#if HOOK_NSURL_CREATION
    // Hook NSURL creation
    Class urlClass = [NSURL class];
    if (urlClass) {
        Method method = class_getClassMethod(urlClass, @selector(URLWithString:));
        if (method) {
            original_initWithURL = method_getImplementation(method);
            method_setImplementation(method, (IMP)hooked_initWithString);
#if BINANCE_REDIRECT_LOGGING
            NSLog(@"%@ Hooked NSURL URLWithString:", @LOG_PREFIX);
#endif
        }
    }
#endif

#if HOOK_NSURLREQUEST_INIT
    // Hook NSURLRequest
    Class requestClass = [NSURLRequest class];
    if (requestClass) {
        Method method = class_getInstanceMethod(requestClass, @selector(initWithURL:));
        if (method) {
            original_initWithRequest = method_getImplementation(method);
            method_setImplementation(method, (IMP)hooked_initWithURL);
#if BINANCE_REDIRECT_LOGGING
            NSLog(@"%@ Hooked NSURLRequest initWithURL:", @LOG_PREFIX);
#endif
        }
    }
#endif

#if HOOK_NSURLSESSION_DATATASK
    // Hook NSURLSession
    Class sessionClass = [NSURLSession class];
    if (sessionClass) {
        // Hook dataTaskWithURL:completionHandler:
        Method dataTaskURLMethod = class_getInstanceMethod(sessionClass, @selector(dataTaskWithURL:completionHandler:));
        if (dataTaskURLMethod) {
            original_dataTaskWithURL = method_getImplementation(dataTaskURLMethod);
            method_setImplementation(dataTaskURLMethod, (IMP)hooked_dataTaskWithURL);
#if BINANCE_REDIRECT_LOGGING
            NSLog(@"%@ Hooked NSURLSession dataTaskWithURL:completionHandler:", @LOG_PREFIX);
#endif
        }

        // Hook dataTaskWithRequest:completionHandler:
        Method dataTaskRequestMethod = class_getInstanceMethod(sessionClass, @selector(dataTaskWithRequest:completionHandler:));
        if (dataTaskRequestMethod) {
            original_dataTaskWithRequest = method_getImplementation(dataTaskRequestMethod);
            method_setImplementation(dataTaskRequestMethod, (IMP)hooked_dataTaskWithRequest);
#if BINANCE_REDIRECT_LOGGING
            NSLog(@"%@ Hooked NSURLSession dataTaskWithRequest:completionHandler:", @LOG_PREFIX);
#endif
        }
    }
#endif

#if BINANCE_REDIRECT_LOGGING
    NSLog(@"%@ All hooks installed successfully", @LOG_PREFIX);
    NSLog(@"%@ Configuration: Logging=%d, Caching=%d, HTTPS Verify=%d", @LOG_PREFIX,
          BINANCE_REDIRECT_LOGGING, CACHE_REDIRECTED_URLS, VERIFY_HTTPS_REDIRECT);
#endif
}
