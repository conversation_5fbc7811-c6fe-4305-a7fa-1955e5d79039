#import <Foundation/Foundation.h>
#import <objc/runtime.h>
#import <dlfcn.h>
#import "config.h"

// Original method implementations
static IMP original_URLWithString = NULL;
static IMP original_initWithURL = NULL;
static IMP original_initWithRequest = NULL;
static IMP original_dataTaskWithURL = NULL;
static IMP original_dataTaskWithRequest = NULL;
static IMP original_requestWithURL = NULL;
static IMP original_mutableRequestWithURL = NULL;
static IMP original_downloadTaskWithURL = NULL;
static IMP original_downloadTaskWithRequest = NULL;
static IMP original_uploadTaskWithRequest = NULL;

// URL cache for performance optimization
#if CACHE_REDIRECTED_URLS
static NSMutableDictionary* urlCache = nil;
static dispatch_queue_t cacheQueue = nil;
#endif

// Helper function to check if domain should be excluded from redirection
BOOL shouldExcludeDomain(NSString* domain) {
    if (!domain) return YES;

    // Convert to lowercase for comparison
    NSString* lowerDomain = [domain lowercaseString];

    for (int i = 0; i < EXCLUDED_DOMAINS_COUNT; i++) {
        NSString* excludedDomain = [[NSString stringWithUTF8String:EXCLUDED_DOMAINS[i]] lowercaseString];
        if ([lowerDomain isEqualToString:excludedDomain] || [lowerDomain hasSuffix:[@"." stringByAppendingString:excludedDomain]]) {
            return YES;
        }
    }

    return NO;
}

// Helper function to redirect ALL URLs to proxy server
NSString* redirectToProxy(NSString* originalURL) {
    if (!originalURL || [originalURL length] == 0) return originalURL;

#if CACHE_REDIRECTED_URLS
    // Check cache first
    if (urlCache) {
        __block NSString* cached = nil;
        dispatch_sync(cacheQueue, ^{
            cached = [urlCache objectForKey:originalURL];
        });
        if (cached) {
            return cached;
        }
    }
#endif

    // Check if already redirected to proxy
    if ([originalURL hasPrefix:@PROXY_SERVER]) {
        return originalURL;
    }

    NSURL* url = [NSURL URLWithString:originalURL];
    if (!url || !url.host) {
        return originalURL; // Invalid URL, return as-is
    }

    // Check if domain should be excluded
    if (shouldExcludeDomain(url.host)) {
#if VERBOSE_LOGGING
        NSLog(@"%@ Excluding domain from redirection: %@", @LOG_PREFIX, url.host);
#endif
        return originalURL;
    }

    // Build proxy URL: https://proxy.x3r0x.me/original-domain/path
    NSString* proxyURL;
    NSString* path = url.path ? url.path : @"";
    NSString* query = url.query ? [@"?" stringByAppendingString:url.query] : @"";
    NSString* fragment = url.fragment ? [@"#" stringByAppendingString:url.fragment] : @"";

    // Remove leading slash from path if present
    if ([path hasPrefix:@"/"]) {
        path = [path substringFromIndex:1];
    }

    if ([path length] > 0) {
        proxyURL = [NSString stringWithFormat:@"%@/%@/%@%@%@", @PROXY_SERVER, url.host, path, query, fragment];
    } else {
        proxyURL = [NSString stringWithFormat:@"%@/%@%@%@", @PROXY_SERVER, url.host, query, fragment];
    }

#if BINANCE_REDIRECT_LOGGING
#if LOG_SENSITIVE_DATA
    NSLog(@"%@ Redirected URL: %@ -> %@", @LOG_PREFIX, originalURL, proxyURL);
#else
    NSLog(@"%@ URL redirected (domain: %@)", @LOG_PREFIX, url.host);
#endif
#endif

#if CACHE_REDIRECTED_URLS
    // Cache the result
    if (urlCache) {
        dispatch_async(cacheQueue, ^{
            if ([urlCache count] >= MAX_CACHE_SIZE) {
                [urlCache removeAllObjects];  // Simple cache eviction
            }
            [urlCache setObject:proxyURL forKey:originalURL];
        });
    }
#endif

    return proxyURL;
}

// Hooked NSURL class method
NSURL* hooked_URLWithString(id self, SEL _cmd, NSString* URLString) {
#if VERBOSE_LOGGING
    NSLog(@"%@ NSURL URLWithString called with: %@", @LOG_PREFIX, URLString);
#endif
    NSString* redirectedURL = redirectToProxy(URLString);
    if (![redirectedURL isEqualToString:URLString]) {
        NSLog(@"%@ NSURL redirected: %@ -> %@", @LOG_PREFIX, URLString, redirectedURL);
    }
    return ((NSURL* (*)(id, SEL, NSString*))original_URLWithString)(self, _cmd, redirectedURL);
}

// Hooked NSURLRequest instance method
NSURLRequest* hooked_initWithURL(id self, SEL _cmd, NSURL* URL) {
#if VERBOSE_LOGGING
    NSLog(@"%@ NSURLRequest initWithURL called with: %@", @LOG_PREFIX, [URL absoluteString]);
#endif
    NSString* redirectedURLString = redirectToProxy([URL absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[URL absoluteString]]) {
        NSLog(@"%@ NSURLRequest redirected: %@ -> %@", @LOG_PREFIX, [URL absoluteString], redirectedURLString);
    }
    return ((NSURLRequest* (*)(id, SEL, NSURL*))original_initWithURL)(self, _cmd, redirectedURL);
}

// Hooked NSURLRequest class method
NSURLRequest* hooked_requestWithURL(id self, SEL _cmd, NSURL* URL) {
#if VERBOSE_LOGGING
    NSLog(@"%@ NSURLRequest requestWithURL called with: %@", @LOG_PREFIX, [URL absoluteString]);
#endif
    NSString* redirectedURLString = redirectToProxy([URL absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[URL absoluteString]]) {
        NSLog(@"%@ NSURLRequest redirected: %@ -> %@", @LOG_PREFIX, [URL absoluteString], redirectedURLString);
    }
    return ((NSURLRequest* (*)(id, SEL, NSURL*))original_requestWithURL)(self, _cmd, redirectedURL);
}

// Hooked NSURLSession dataTask methods
NSURLSessionDataTask* hooked_dataTaskWithURL(id self, SEL _cmd, NSURL* url, void (^completionHandler)(NSData*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"%@ NSURLSession dataTaskWithURL called with: %@", @LOG_PREFIX, [url absoluteString]);
#endif
    NSString* redirectedURLString = redirectToProxy([url absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[url absoluteString]]) {
        NSLog(@"%@ NSURLSession dataTaskWithURL redirected: %@ -> %@", @LOG_PREFIX, [url absoluteString], redirectedURLString);
    }
    return ((NSURLSessionDataTask* (*)(id, SEL, NSURL*, void (^)(NSData*, NSURLResponse*, NSError*)))original_dataTaskWithURL)(self, _cmd, redirectedURL, completionHandler);
}

NSURLSessionDataTask* hooked_dataTaskWithRequest(id self, SEL _cmd, NSURLRequest* request, void (^completionHandler)(NSData*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"%@ NSURLSession dataTaskWithRequest called with: %@", @LOG_PREFIX, [[request URL] absoluteString]);
#endif
    NSString* redirectedURLString = redirectToProxy([[request URL] absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    NSMutableURLRequest* mutableRequest = [request mutableCopy];
    [mutableRequest setURL:redirectedURL];

    if (![redirectedURLString isEqualToString:[[request URL] absoluteString]]) {
        NSLog(@"%@ NSURLSession dataTaskWithRequest redirected: %@ -> %@", @LOG_PREFIX, [[request URL] absoluteString], redirectedURLString);
    }

    return ((NSURLSessionDataTask* (*)(id, SEL, NSURLRequest*, void (^)(NSData*, NSURLResponse*, NSError*)))original_dataTaskWithRequest)(self, _cmd, mutableRequest, completionHandler);
}

// Additional NSURLRequest hooks
NSMutableURLRequest* hooked_mutableRequestWithURL(id self, SEL _cmd, NSURL* URL) {
#if VERBOSE_LOGGING
    NSLog(@"%@ NSMutableURLRequest mutableRequestWithURL called with: %@", @LOG_PREFIX, [URL absoluteString]);
#endif
    NSString* redirectedURLString = redirectToProxy([URL absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[URL absoluteString]]) {
        NSLog(@"%@ NSMutableURLRequest redirected: %@ -> %@", @LOG_PREFIX, [URL absoluteString], redirectedURLString);
    }
    return ((NSMutableURLRequest* (*)(id, SEL, NSURL*))original_mutableRequestWithURL)(self, _cmd, redirectedURL);
}

// Additional NSURLSession hooks
NSURLSessionDownloadTask* hooked_downloadTaskWithURL(id self, SEL _cmd, NSURL* url, void (^completionHandler)(NSURL*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"%@ NSURLSession downloadTaskWithURL called with: %@", @LOG_PREFIX, [url absoluteString]);
#endif
    NSString* redirectedURLString = redirectToProxy([url absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[url absoluteString]]) {
        NSLog(@"%@ NSURLSession downloadTaskWithURL redirected: %@ -> %@", @LOG_PREFIX, [url absoluteString], redirectedURLString);
    }
    return ((NSURLSessionDownloadTask* (*)(id, SEL, NSURL*, void (^)(NSURL*, NSURLResponse*, NSError*)))original_downloadTaskWithURL)(self, _cmd, redirectedURL, completionHandler);
}

NSURLSessionDownloadTask* hooked_downloadTaskWithRequest(id self, SEL _cmd, NSURLRequest* request, void (^completionHandler)(NSURL*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"%@ NSURLSession downloadTaskWithRequest called with: %@", @LOG_PREFIX, [[request URL] absoluteString]);
#endif
    NSString* redirectedURLString = redirectToProxy([[request URL] absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    NSMutableURLRequest* mutableRequest = [request mutableCopy];
    [mutableRequest setURL:redirectedURL];

    if (![redirectedURLString isEqualToString:[[request URL] absoluteString]]) {
        NSLog(@"%@ NSURLSession downloadTaskWithRequest redirected: %@ -> %@", @LOG_PREFIX, [[request URL] absoluteString], redirectedURLString);
    }

    return ((NSURLSessionDownloadTask* (*)(id, SEL, NSURLRequest*, void (^)(NSURL*, NSURLResponse*, NSError*)))original_downloadTaskWithRequest)(self, _cmd, mutableRequest, completionHandler);
}

NSURLSessionUploadTask* hooked_uploadTaskWithRequest(id self, SEL _cmd, NSURLRequest* request, NSData* bodyData, void (^completionHandler)(NSData*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"%@ NSURLSession uploadTaskWithRequest called with: %@", @LOG_PREFIX, [[request URL] absoluteString]);
#endif
    NSString* redirectedURLString = redirectToProxy([[request URL] absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    NSMutableURLRequest* mutableRequest = [request mutableCopy];
    [mutableRequest setURL:redirectedURL];

    if (![redirectedURLString isEqualToString:[[request URL] absoluteString]]) {
        NSLog(@"%@ NSURLSession uploadTaskWithRequest redirected: %@ -> %@", @LOG_PREFIX, [[request URL] absoluteString], redirectedURLString);
    }

    return ((NSURLSessionUploadTask* (*)(id, SEL, NSURLRequest*, NSData*, void (^)(NSData*, NSURLResponse*, NSError*)))original_uploadTaskWithRequest)(self, _cmd, mutableRequest, bodyData, completionHandler);
}

// Proper method swizzling helper
void swizzleMethod(Class targetClass, SEL originalSelector, IMP newImplementation, IMP* originalIMP, BOOL isClassMethod) {
    Method originalMethod;

    if (isClassMethod) {
        originalMethod = class_getClassMethod(targetClass, originalSelector);
    } else {
        originalMethod = class_getInstanceMethod(targetClass, originalSelector);
    }

    if (originalMethod) {
        *originalIMP = method_getImplementation(originalMethod);
        method_setImplementation(originalMethod, newImplementation);
        NSLog(@"%@ Successfully swizzled %@ %@", @LOG_PREFIX,
              isClassMethod ? @"+" : @"-", NSStringFromSelector(originalSelector));
    } else {
        NSLog(@"%@ Failed to find method %@ %@", @LOG_PREFIX,
              isClassMethod ? @"+" : @"-", NSStringFromSelector(originalSelector));
    }
}

// Constructor - called when dylib is loaded
__attribute__((constructor))
static void initialize() {
#if BINANCE_REDIRECT_LOGGING
    NSLog(@"%@ Dylib v%@ loaded - initializing URL redirection hooks", @LOG_PREFIX, @BINANCE_REDIRECT_VERSION);
    NSLog(@"%@ Build: %s", @LOG_PREFIX, BINANCE_REDIRECT_BUILD_DATE);
#endif

#if CACHE_REDIRECTED_URLS
    // Initialize URL cache
    urlCache = [[NSMutableDictionary alloc] init];
    cacheQueue = dispatch_queue_create("com.binanceredirect.cache", DISPATCH_QUEUE_SERIAL);
#endif

#if HOOK_NSURL_CREATION
    // Hook NSURL class method URLWithString:
    swizzleMethod([NSURL class], @selector(URLWithString:), (IMP)hooked_URLWithString, &original_URLWithString, YES);
#endif

#if HOOK_NSURLREQUEST_INIT
    // Hook NSURLRequest instance method initWithURL:
    swizzleMethod([NSURLRequest class], @selector(initWithURL:), (IMP)hooked_initWithURL, &original_initWithURL, NO);

    // Hook NSURLRequest class method requestWithURL:
    swizzleMethod([NSURLRequest class], @selector(requestWithURL:), (IMP)hooked_requestWithURL, &original_requestWithURL, YES);

    // Hook NSMutableURLRequest class method mutableRequestWithURL:
    swizzleMethod([NSMutableURLRequest class], @selector(requestWithURL:), (IMP)hooked_mutableRequestWithURL, &original_mutableRequestWithURL, YES);
#endif

#if HOOK_NSURLSESSION_DATATASK
    // Hook NSURLSession instance methods
    swizzleMethod([NSURLSession class], @selector(dataTaskWithURL:completionHandler:), (IMP)hooked_dataTaskWithURL, &original_dataTaskWithURL, NO);
    swizzleMethod([NSURLSession class], @selector(dataTaskWithRequest:completionHandler:), (IMP)hooked_dataTaskWithRequest, &original_dataTaskWithRequest, NO);

    // Hook additional NSURLSession methods
    swizzleMethod([NSURLSession class], @selector(downloadTaskWithURL:completionHandler:), (IMP)hooked_downloadTaskWithURL, &original_downloadTaskWithURL, NO);
    swizzleMethod([NSURLSession class], @selector(downloadTaskWithRequest:completionHandler:), (IMP)hooked_downloadTaskWithRequest, &original_downloadTaskWithRequest, NO);
    swizzleMethod([NSURLSession class], @selector(uploadTaskWithRequest:fromData:completionHandler:), (IMP)hooked_uploadTaskWithRequest, &original_uploadTaskWithRequest, NO);
#endif

#if BINANCE_REDIRECT_LOGGING
    NSLog(@"%@ All hooks installed successfully", @LOG_PREFIX);
    NSLog(@"%@ Configuration: Logging=%d, Caching=%d, HTTPS Verify=%d", @LOG_PREFIX,
          BINANCE_REDIRECT_LOGGING, CACHE_REDIRECTED_URLS, VERIFY_HTTPS_REDIRECT);
#endif
}
