#import <Foundation/Foundation.h>
#import <objc/runtime.h>
#import <dlfcn.h>
#import "config.h"

// Original method implementations
static IMP original_URLWithString = NULL;
static IMP original_initWithURL = NULL;
static IMP original_initWithRequest = NULL;
static IMP original_dataTaskWithURL = NULL;
static IMP original_dataTaskWithRequest = NULL;
static IMP original_requestWithURL = NULL;
static IMP original_mutableRequestWithURL = NULL;
static IMP original_downloadTaskWithURL = NULL;
static IMP original_downloadTaskWithRequest = NULL;
static IMP original_uploadTaskWithRequest = NULL;

// URL cache for performance optimization
#if CACHE_REDIRECTED_URLS
static NSMutableDictionary* urlCache = nil;
static dispatch_queue_t cacheQueue = nil;
#endif

// Helper function to redirect Binance URLs
NSString* redirectBinanceURL(NSString* originalURL) {
    if (!originalURL) return originalURL;

#if CACHE_REDIRECTED_URLS
    // Check cache first
    if (urlCache) {
        __block NSString* cached = nil;
        dispatch_sync(cacheQueue, ^{
            cached = [urlCache objectForKey:originalURL];
        });
        if (cached) {
            return cached;
        }
    }
#endif

    NSString* urlString = [originalURL copy];
    BOOL wasRedirected = NO;

    // Check if URL contains binance domain and hasn't been redirected already
    if ([urlString containsString:@".binance."] && ![urlString containsString:@".x3r0x.me"]) {
        // Apply specific domain mappings first
        for (int i = 0; i < DOMAIN_MAPPINGS_COUNT; i++) {
            NSString* original = [NSString stringWithUTF8String:DOMAIN_MAPPINGS[i].original];
            NSString* replacement = [NSString stringWithUTF8String:DOMAIN_MAPPINGS[i].replacement];

            if ([urlString containsString:original]) {
                urlString = [urlString stringByReplacingOccurrencesOfString:original
                                                                withString:replacement];
                wasRedirected = YES;
                break; // Stop after first match to avoid double replacement
            }
        }

#if ENABLE_WILDCARD_REDIRECT
        // Handle other binance subdomains with wildcard pattern
        if (!wasRedirected) {
            NSRegularExpression* regex = [NSRegularExpression regularExpressionWithPattern:BINANCE_DOMAIN_PATTERN
                                                                                   options:0
                                                                                     error:nil];
            if (regex) {
                NSString* newString = [regex stringByReplacingMatchesInString:urlString
                                                                      options:0
                                                                        range:NSMakeRange(0, [urlString length])
                                                                 withTemplate:BINANCE_REPLACEMENT_TEMPLATE];
                if (![newString isEqualToString:urlString]) {
                    urlString = newString;
                    wasRedirected = YES;
                }
            }
        }
#endif

#if VERIFY_HTTPS_REDIRECT
        // Ensure HTTPS URLs remain HTTPS
        if (wasRedirected && [originalURL hasPrefix:@"https://"] && ![urlString hasPrefix:@"https://"]) {
            urlString = [urlString stringByReplacingOccurrencesOfString:@"http://" withString:@"https://"];
        }
#endif

#if BINANCE_REDIRECT_LOGGING
        if (wasRedirected) {
#if LOG_SENSITIVE_DATA
            NSLog(@"%@ Redirected URL: %@ -> %@", @LOG_PREFIX, originalURL, urlString);
#else
            NSLog(@"%@ URL redirected (domain: %@)", @LOG_PREFIX, [[NSURL URLWithString:originalURL] host]);
#endif
        }
#endif
    }

#if CACHE_REDIRECTED_URLS
    // Cache the result
    if (urlCache && wasRedirected) {
        dispatch_async(cacheQueue, ^{
            if ([urlCache count] >= MAX_CACHE_SIZE) {
                [urlCache removeAllObjects];  // Simple cache eviction
            }
            [urlCache setObject:urlString forKey:originalURL];
        });
    }
#endif

    return urlString;
}

// Hooked NSURL class method
NSURL* hooked_URLWithString(id self, SEL _cmd, NSString* URLString) {
#if VERBOSE_LOGGING
    NSLog(@"[BinanceRedirect] NSURL URLWithString called with: %@", URLString);
#endif
    NSString* redirectedURL = redirectBinanceURL(URLString);
    if (![redirectedURL isEqualToString:URLString]) {
        NSLog(@"[BinanceRedirect] NSURL redirected: %@ -> %@", URLString, redirectedURL);
    }
    return ((NSURL* (*)(id, SEL, NSString*))original_URLWithString)(self, _cmd, redirectedURL);
}

// Hooked NSURLRequest instance method
NSURLRequest* hooked_initWithURL(id self, SEL _cmd, NSURL* URL) {
#if VERBOSE_LOGGING
    NSLog(@"[BinanceRedirect] NSURLRequest initWithURL called with: %@", [URL absoluteString]);
#endif
    NSString* redirectedURLString = redirectBinanceURL([URL absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[URL absoluteString]]) {
        NSLog(@"[BinanceRedirect] NSURLRequest redirected: %@ -> %@", [URL absoluteString], redirectedURLString);
    }
    return ((NSURLRequest* (*)(id, SEL, NSURL*))original_initWithURL)(self, _cmd, redirectedURL);
}

// Hooked NSURLRequest class method
NSURLRequest* hooked_requestWithURL(id self, SEL _cmd, NSURL* URL) {
#if VERBOSE_LOGGING
    NSLog(@"[BinanceRedirect] NSURLRequest requestWithURL called with: %@", [URL absoluteString]);
#endif
    NSString* redirectedURLString = redirectBinanceURL([URL absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[URL absoluteString]]) {
        NSLog(@"[BinanceRedirect] NSURLRequest redirected: %@ -> %@", [URL absoluteString], redirectedURLString);
    }
    return ((NSURLRequest* (*)(id, SEL, NSURL*))original_requestWithURL)(self, _cmd, redirectedURL);
}

// Hooked NSURLSession dataTask methods
NSURLSessionDataTask* hooked_dataTaskWithURL(id self, SEL _cmd, NSURL* url, void (^completionHandler)(NSData*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"[BinanceRedirect] NSURLSession dataTaskWithURL called with: %@", [url absoluteString]);
#endif
    NSString* redirectedURLString = redirectBinanceURL([url absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[url absoluteString]]) {
        NSLog(@"[BinanceRedirect] NSURLSession dataTaskWithURL redirected: %@ -> %@", [url absoluteString], redirectedURLString);
    }
    return ((NSURLSessionDataTask* (*)(id, SEL, NSURL*, void (^)(NSData*, NSURLResponse*, NSError*)))original_dataTaskWithURL)(self, _cmd, redirectedURL, completionHandler);
}

NSURLSessionDataTask* hooked_dataTaskWithRequest(id self, SEL _cmd, NSURLRequest* request, void (^completionHandler)(NSData*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"[BinanceRedirect] NSURLSession dataTaskWithRequest called with: %@", [[request URL] absoluteString]);
#endif
    NSString* redirectedURLString = redirectBinanceURL([[request URL] absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    NSMutableURLRequest* mutableRequest = [request mutableCopy];
    [mutableRequest setURL:redirectedURL];

    if (![redirectedURLString isEqualToString:[[request URL] absoluteString]]) {
        NSLog(@"[BinanceRedirect] NSURLSession dataTaskWithRequest redirected: %@ -> %@", [[request URL] absoluteString], redirectedURLString);
    }

    return ((NSURLSessionDataTask* (*)(id, SEL, NSURLRequest*, void (^)(NSData*, NSURLResponse*, NSError*)))original_dataTaskWithRequest)(self, _cmd, mutableRequest, completionHandler);
}

// Additional NSURLRequest hooks
NSMutableURLRequest* hooked_mutableRequestWithURL(id self, SEL _cmd, NSURL* URL) {
#if VERBOSE_LOGGING
    NSLog(@"[BinanceRedirect] NSMutableURLRequest mutableRequestWithURL called with: %@", [URL absoluteString]);
#endif
    NSString* redirectedURLString = redirectBinanceURL([URL absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[URL absoluteString]]) {
        NSLog(@"[BinanceRedirect] NSMutableURLRequest redirected: %@ -> %@", [URL absoluteString], redirectedURLString);
    }
    return ((NSMutableURLRequest* (*)(id, SEL, NSURL*))original_mutableRequestWithURL)(self, _cmd, redirectedURL);
}

// Additional NSURLSession hooks
NSURLSessionDownloadTask* hooked_downloadTaskWithURL(id self, SEL _cmd, NSURL* url, void (^completionHandler)(NSURL*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"[BinanceRedirect] NSURLSession downloadTaskWithURL called with: %@", [url absoluteString]);
#endif
    NSString* redirectedURLString = redirectBinanceURL([url absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    if (![redirectedURLString isEqualToString:[url absoluteString]]) {
        NSLog(@"[BinanceRedirect] NSURLSession downloadTaskWithURL redirected: %@ -> %@", [url absoluteString], redirectedURLString);
    }
    return ((NSURLSessionDownloadTask* (*)(id, SEL, NSURL*, void (^)(NSURL*, NSURLResponse*, NSError*)))original_downloadTaskWithURL)(self, _cmd, redirectedURL, completionHandler);
}

NSURLSessionDownloadTask* hooked_downloadTaskWithRequest(id self, SEL _cmd, NSURLRequest* request, void (^completionHandler)(NSURL*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"[BinanceRedirect] NSURLSession downloadTaskWithRequest called with: %@", [[request URL] absoluteString]);
#endif
    NSString* redirectedURLString = redirectBinanceURL([[request URL] absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    NSMutableURLRequest* mutableRequest = [request mutableCopy];
    [mutableRequest setURL:redirectedURL];

    if (![redirectedURLString isEqualToString:[[request URL] absoluteString]]) {
        NSLog(@"[BinanceRedirect] NSURLSession downloadTaskWithRequest redirected: %@ -> %@", [[request URL] absoluteString], redirectedURLString);
    }

    return ((NSURLSessionDownloadTask* (*)(id, SEL, NSURLRequest*, void (^)(NSURL*, NSURLResponse*, NSError*)))original_downloadTaskWithRequest)(self, _cmd, mutableRequest, completionHandler);
}

NSURLSessionUploadTask* hooked_uploadTaskWithRequest(id self, SEL _cmd, NSURLRequest* request, NSData* bodyData, void (^completionHandler)(NSData*, NSURLResponse*, NSError*)) {
#if VERBOSE_LOGGING
    NSLog(@"[BinanceRedirect] NSURLSession uploadTaskWithRequest called with: %@", [[request URL] absoluteString]);
#endif
    NSString* redirectedURLString = redirectBinanceURL([[request URL] absoluteString]);
    NSURL* redirectedURL = [NSURL URLWithString:redirectedURLString];
    NSMutableURLRequest* mutableRequest = [request mutableCopy];
    [mutableRequest setURL:redirectedURL];

    if (![redirectedURLString isEqualToString:[[request URL] absoluteString]]) {
        NSLog(@"[BinanceRedirect] NSURLSession uploadTaskWithRequest redirected: %@ -> %@", [[request URL] absoluteString], redirectedURLString);
    }

    return ((NSURLSessionUploadTask* (*)(id, SEL, NSURLRequest*, NSData*, void (^)(NSData*, NSURLResponse*, NSError*)))original_uploadTaskWithRequest)(self, _cmd, mutableRequest, bodyData, completionHandler);
}

// Proper method swizzling helper
void swizzleMethod(Class targetClass, SEL originalSelector, IMP newImplementation, IMP* originalIMP, BOOL isClassMethod) {
    Method originalMethod;

    if (isClassMethod) {
        originalMethod = class_getClassMethod(targetClass, originalSelector);
    } else {
        originalMethod = class_getInstanceMethod(targetClass, originalSelector);
    }

    if (originalMethod) {
        *originalIMP = method_getImplementation(originalMethod);
        method_setImplementation(originalMethod, newImplementation);
        NSLog(@"[BinanceRedirect] Successfully swizzled %@ %@",
              isClassMethod ? @"+" : @"-", NSStringFromSelector(originalSelector));
    } else {
        NSLog(@"[BinanceRedirect] Failed to find method %@ %@",
              isClassMethod ? @"+" : @"-", NSStringFromSelector(originalSelector));
    }
}

// Constructor - called when dylib is loaded
__attribute__((constructor))
static void initialize() {
#if BINANCE_REDIRECT_LOGGING
    NSLog(@"%@ Dylib v%@ loaded - initializing URL redirection hooks", @LOG_PREFIX, @BINANCE_REDIRECT_VERSION);
    NSLog(@"%@ Build: %s", @LOG_PREFIX, BINANCE_REDIRECT_BUILD_DATE);
#endif

#if CACHE_REDIRECTED_URLS
    // Initialize URL cache
    urlCache = [[NSMutableDictionary alloc] init];
    cacheQueue = dispatch_queue_create("com.binanceredirect.cache", DISPATCH_QUEUE_SERIAL);
#endif

#if HOOK_NSURL_CREATION
    // Hook NSURL class method URLWithString:
    swizzleMethod([NSURL class], @selector(URLWithString:), (IMP)hooked_URLWithString, &original_URLWithString, YES);
#endif

#if HOOK_NSURLREQUEST_INIT
    // Hook NSURLRequest instance method initWithURL:
    swizzleMethod([NSURLRequest class], @selector(initWithURL:), (IMP)hooked_initWithURL, &original_initWithURL, NO);

    // Hook NSURLRequest class method requestWithURL:
    swizzleMethod([NSURLRequest class], @selector(requestWithURL:), (IMP)hooked_requestWithURL, &original_requestWithURL, YES);

    // Hook NSMutableURLRequest class method mutableRequestWithURL:
    swizzleMethod([NSMutableURLRequest class], @selector(requestWithURL:), (IMP)hooked_mutableRequestWithURL, &original_mutableRequestWithURL, YES);
#endif

#if HOOK_NSURLSESSION_DATATASK
    // Hook NSURLSession instance methods
    swizzleMethod([NSURLSession class], @selector(dataTaskWithURL:completionHandler:), (IMP)hooked_dataTaskWithURL, &original_dataTaskWithURL, NO);
    swizzleMethod([NSURLSession class], @selector(dataTaskWithRequest:completionHandler:), (IMP)hooked_dataTaskWithRequest, &original_dataTaskWithRequest, NO);

    // Hook additional NSURLSession methods
    swizzleMethod([NSURLSession class], @selector(downloadTaskWithURL:completionHandler:), (IMP)hooked_downloadTaskWithURL, &original_downloadTaskWithURL, NO);
    swizzleMethod([NSURLSession class], @selector(downloadTaskWithRequest:completionHandler:), (IMP)hooked_downloadTaskWithRequest, &original_downloadTaskWithRequest, NO);
    swizzleMethod([NSURLSession class], @selector(uploadTaskWithRequest:fromData:completionHandler:), (IMP)hooked_uploadTaskWithRequest, &original_uploadTaskWithRequest, NO);
#endif

#if BINANCE_REDIRECT_LOGGING
    NSLog(@"%@ All hooks installed successfully", @LOG_PREFIX);
    NSLog(@"%@ Configuration: Logging=%d, Caching=%d, HTTPS Verify=%d", @LOG_PREFIX,
          BINANCE_REDIRECT_LOGGING, CACHE_REDIRECTED_URLS, VERIFY_HTTPS_REDIRECT);
#endif
}
