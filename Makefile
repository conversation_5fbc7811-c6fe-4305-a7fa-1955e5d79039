# Makefile for BinanceRedirect dylib
# This creates a dynamic library for iOS that can be injected into IPA files

# Configuration
TARGET = BinanceRedirect
DYLIB_NAME = lib$(TARGET).dylib
SOURCE = $(TARGET).mm

# iOS SDK paths - adjust these based on your Xcode installation
XCODE_PATH = $(shell xcode-select -p)
IOS_SDK_PATH = $(XCODE_PATH)/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk
IOS_SIM_SDK_PATH = $(XCODE_PATH)/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk

# Compiler settings
CC = clang
CFLAGS = -arch arm64 -isysroot $(IOS_SDK_PATH) -miphoneos-version-min=12.0
CFLAGS += -fPIC -shared -framework Foundation -framework UIKit
CFLAGS += -fobjc-arc -O2

# Simulator build flags (for testing)
SIM_CFLAGS = -arch x86_64 -isysroot $(IOS_SIM_SDK_PATH) -mios-simulator-version-min=12.0
SIM_CFLAGS += -fPIC -shared -framework Foundation -framework UIKit
SIM_CFLAGS += -fobjc-arc -O2

# Default target
all: device

# Build for device (ARM64)
device: $(DYLIB_NAME)

$(DYLIB_NAME): $(SOURCE)
	@echo "Building $(DYLIB_NAME) for iOS device (ARM64)..."
	$(CC) $(CFLAGS) -o $@ $<
	@echo "Successfully built $(DYLIB_NAME)"
	@echo "File info:"
	@file $(DYLIB_NAME)
	@echo "Size: $$(du -h $(DYLIB_NAME) | cut -f1)"

# Build for simulator (x86_64) - useful for testing
simulator: lib$(TARGET)_sim.dylib

lib$(TARGET)_sim.dylib: $(SOURCE)
	@echo "Building lib$(TARGET)_sim.dylib for iOS Simulator (x86_64)..."
	$(CC) $(SIM_CFLAGS) -o $@ $<
	@echo "Successfully built lib$(TARGET)_sim.dylib"

# Universal binary (both architectures)
universal: lib$(TARGET)_universal.dylib

lib$(TARGET)_universal.dylib: $(DYLIB_NAME) lib$(TARGET)_sim.dylib
	@echo "Creating universal binary..."
	lipo -create -output $@ $(DYLIB_NAME) lib$(TARGET)_sim.dylib
	@echo "Successfully created universal binary"
	@lipo -info $@

# Clean build artifacts
clean:
	rm -f *.dylib
	@echo "Cleaned build artifacts"

# Install to common injection directory (optional)
install: $(DYLIB_NAME)
	@echo "Installing $(DYLIB_NAME)..."
	@mkdir -p ~/Desktop/dylibs
	cp $(DYLIB_NAME) ~/Desktop/dylibs/
	@echo "Installed to ~/Desktop/dylibs/"

# Show build information
info:
	@echo "Build Configuration:"
	@echo "  Target: $(TARGET)"
	@echo "  Output: $(DYLIB_NAME)"
	@echo "  Source: $(SOURCE)"
	@echo "  iOS SDK: $(IOS_SDK_PATH)"
	@echo "  Simulator SDK: $(IOS_SIM_SDK_PATH)"
	@echo ""
	@echo "Available targets:"
	@echo "  device     - Build for iOS device (ARM64)"
	@echo "  simulator  - Build for iOS Simulator (x86_64)"
	@echo "  universal  - Build universal binary"
	@echo "  clean      - Remove build artifacts"
	@echo "  install    - Install to ~/Desktop/dylibs/"
	@echo "  info       - Show this information"

# Check if Xcode is installed
check:
	@echo "Checking build environment..."
	@if [ ! -d "$(XCODE_PATH)" ]; then \
		echo "ERROR: Xcode not found. Please install Xcode."; \
		exit 1; \
	fi
	@if [ ! -d "$(IOS_SDK_PATH)" ]; then \
		echo "ERROR: iOS SDK not found at $(IOS_SDK_PATH)"; \
		exit 1; \
	fi
	@echo "✓ Xcode found at: $(XCODE_PATH)"
	@echo "✓ iOS SDK found at: $(IOS_SDK_PATH)"
	@echo "✓ Build environment is ready"

.PHONY: all device simulator universal clean install info check
