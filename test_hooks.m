#import <Foundation/Foundation.h>

// Test program to verify that our hooks are working
// This simulates the network calls that an iOS app might make

int main(int argc, const char * argv[]) {
    @autoreleasepool {
        NSLog(@"🧪 Testing BinanceRedirect Hooks");
        NSLog(@"================================");
        
        // Test 1: NSURL URLWithString
        NSLog(@"\n1. Testing NSURL URLWithString:");
        NSURL *url1 = [NSURL URLWithString:@"https://api.binance.com/api/v3/ticker"];
        NSLog(@"   Created URL: %@", [url1 absoluteString]);
        
        // Test 2: NSURLRequest requestWithURL
        NSLog(@"\n2. Testing NSURLRequest requestWithURL:");
        NSURLRequest *request1 = [NSURLRequest requestWithURL:url1];
        NSLog(@"   Request URL: %@", [[request1 URL] absoluteString]);
        
        // Test 3: NSURLRequest initWithURL
        NSLog(@"\n3. Testing NSURLRequest initWithURL:");
        NSURL *url2 = [NSURL URLWithString:@"https://native.binance.com/stream"];
        NSURLRequest *request2 = [[NSURLRequest alloc] initWithURL:url2];
        NSLog(@"   Request URL: %@", [[request2 URL] absoluteString]);
        
        // Test 4: NSMutableURLRequest
        NSLog(@"\n4. Testing NSMutableURLRequest:");
        NSURL *url3 = [NSURL URLWithString:@"https://fapi.binance.com/fapi/v1/exchangeInfo"];
        NSMutableURLRequest *mutableRequest = [NSMutableURLRequest requestWithURL:url3];
        NSLog(@"   Mutable Request URL: %@", [[mutableRequest URL] absoluteString]);
        
        // Test 5: NSURLSession dataTask (this won't actually execute, just create the task)
        NSLog(@"\n5. Testing NSURLSession dataTaskWithURL:");
        NSURLSession *session = [NSURLSession sharedSession];
        NSURL *url4 = [NSURL URLWithString:@"https://stream.binance.com/ws/btcusdt@ticker"];
        NSURLSessionDataTask *dataTask = [session dataTaskWithURL:url4 completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
            // This won't execute in our test
        }];
        NSLog(@"   DataTask URL: %@", [[[dataTask originalRequest] URL] absoluteString]);
        
        // Test 6: NSURLSession dataTaskWithRequest
        NSLog(@"\n6. Testing NSURLSession dataTaskWithRequest:");
        NSURL *url5 = [NSURL URLWithString:@"https://www.binance.com/api/v1/ping"];
        NSURLRequest *request3 = [NSURLRequest requestWithURL:url5];
        NSURLSessionDataTask *dataTask2 = [session dataTaskWithRequest:request3 completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
            // This won't execute in our test
        }];
        NSLog(@"   DataTask Request URL: %@", [[[dataTask2 originalRequest] URL] absoluteString]);
        
        // Test 7: Non-Binance URL (should not be redirected)
        NSLog(@"\n7. Testing non-Binance URL:");
        NSURL *url6 = [NSURL URLWithString:@"https://api.coinbase.com/v2/exchange-rates"];
        NSLog(@"   Non-Binance URL: %@", [url6 absoluteString]);
        
        NSLog(@"\n✅ Hook testing completed!");
        NSLog(@"If the dylib is working, you should see redirected URLs above.");
        NSLog(@"Look for URLs ending with '.x3r0x.me' in the console output.");
    }
    
    return 0;
}
