# BinanceRedirect Dylib - Usage Examples

This document provides step-by-step examples of how to use the BinanceRedirect dylib to inject URL redirection into iOS applications.

## Prerequisites

1. **macOS with Xcode** - Required for building the dylib
2. **iOS development setup** - Xcode, iOS SDK, developer account
3. **Injection tools** - One of the following:
   - `insert_dylib` (recommended)
   - `optool`
4. **Code signing tools** - For re-signing the modified IPA

## Step 1: Build the Dylib

### Quick Build
```bash
# Clone or download this project
cd /path/to/BinanceRedirect

# Build for iOS device
make device

# Verify the build
file libBinanceRedirect.dylib
```

### Custom Configuration
If you need to modify the redirection domains:

1. Edit `config.h` to change target domains
2. Rebuild:
```bash
make clean
make device
```

## Step 2: Prepare Your IPA

### Option A: Extract from Device
```bash
# Use tools like 3uTools, iMazing, or iTunes to extract IPA from device
# Or download from App Store using tools like ipatool
```

### Option B: Build from Source
```bash
# If you have the app's source code
xcodebuild -workspace YourApp.xcworkspace -scheme YourApp -configuration Release -archivePath YourApp.xcarchive archive
xcodebuild -exportArchive -archivePath YourApp.xcarchive -exportPath ./export -exportOptionsPlist ExportOptions.plist
```

## Step 3: Inject the Dylib

### Method 1: Using the Python Helper Script
```bash
python inject_helper.py --ipa YourApp.ipa --dylib libBinanceRedirect.dylib --output YourApp_injected.ipa
```

### Method 2: Manual Injection with insert_dylib

1. **Extract the IPA:**
```bash
unzip YourApp.ipa -d extracted/
cd extracted/Payload/YourApp.app/
```

2. **Find the main executable:**
```bash
# Usually has the same name as the app
ls -la
# Look for the file without extension that matches your app name
```

3. **Inject the dylib:**
```bash
insert_dylib @executable_path/libBinanceRedirect.dylib YourAppExecutable
```

4. **Copy dylib to app bundle:**
```bash
cp /path/to/libBinanceRedirect.dylib ./
```

5. **Repackage IPA:**
```bash
cd ../../..
zip -r YourApp_injected.ipa extracted/Payload/
```

### Method 3: Using optool

```bash
# Extract IPA
unzip YourApp.ipa -d extracted/
cd extracted/Payload/YourApp.app/

# Inject with optool
optool install -c load -p "@executable_path/libBinanceRedirect.dylib" -t YourAppExecutable

# Copy dylib
cp /path/to/libBinanceRedirect.dylib ./

# Repackage
cd ../../..
zip -r YourApp_injected.ipa extracted/Payload/
```

## Step 4: Re-sign the IPA

### Using iOS App Signer (GUI)
1. Download iOS App Signer
2. Select your injected IPA
3. Choose your developer certificate
4. Select provisioning profile
5. Click "Start"

### Using Command Line
```bash
# Install ios-deploy and libimobiledevice if needed
brew install ios-deploy libimobiledevice

# Re-sign with your certificate
codesign -f -s "iPhone Developer: Your Name (XXXXXXXXXX)" --entitlements entitlements.plist extracted/Payload/YourApp.app/

# Create new IPA
cd extracted/
zip -r ../YourApp_signed.ipa Payload/
```

## Step 5: Install and Test

### Install on Device
```bash
# Using Xcode
# Drag and drop the IPA onto Xcode's Devices window

# Using ios-deploy
ios-deploy --bundle YourApp_signed.ipa

# Using 3uTools or similar
# Use GUI tool to install IPA
```

### Verify Installation
1. **Check Console Logs:**
   - Open Xcode → Window → Devices and Simulators
   - Select your device → Open Console
   - Look for `[BinanceRedirect]` messages

2. **Expected Log Output:**
```
[BinanceRedirect] Dylib v1.0.0 loaded - initializing URL redirection hooks
[BinanceRedirect] Hooked NSURL URLWithString:
[BinanceRedirect] Hooked NSURLRequest initWithURL:
[BinanceRedirect] Hooked NSURLSession dataTaskWithURL:completionHandler:
[BinanceRedirect] All hooks installed successfully
[BinanceRedirect] URL redirected (domain: native.binance.com)
```

## Step 6: Test Network Redirection

### Monitor Network Traffic
Use tools like:
- **Charles Proxy** - Set up proxy on device to monitor HTTP/HTTPS traffic
- **Wireshark** - Monitor network packets
- **mitmproxy** - Command-line proxy for monitoring

### Test Scenarios
1. **Launch the app** - Should see initialization logs
2. **Trigger network requests** - Use app features that contact Binance
3. **Verify redirection** - Check that requests go to `*.x3r0x.me` domains
4. **Test functionality** - Ensure app still works with redirected URLs

## Troubleshooting

### Common Issues

**1. Dylib not loading:**
```
# Check if dylib is properly injected
otool -L /path/to/app/executable
# Should show: @executable_path/libBinanceRedirect.dylib

# Check if dylib exists in app bundle
ls -la /path/to/app/bundle/
```

**2. App crashes on launch:**
```
# Check crash logs in Xcode Console
# Common causes:
# - Missing code signature
# - Incompatible architecture
# - Missing dependencies
```

**3. No redirection happening:**
```
# Enable verbose logging in config.h:
#define VERBOSE_LOGGING 1
#define LOG_SENSITIVE_DATA 1

# Rebuild and test again
```

**4. Certificate/signing issues:**
```
# Ensure you have valid developer certificate
security find-identity -v -p codesigning

# Check provisioning profile
# Make sure it includes your device UDID
```

### Debug Mode

Enable debug mode for more detailed logging:

1. Edit `config.h`:
```c
#define DEBUG_MODE 1
#define VERBOSE_LOGGING 1
#define LOG_SENSITIVE_DATA 1  // Only for debugging!
```

2. Rebuild and reinstall:
```bash
make clean
make device
# Re-inject and install
```

## Security Considerations

- **Only use on apps you own or have permission to modify**
- **Ensure your redirect domains are secure and under your control**
- **Don't log sensitive data in production**
- **Test thoroughly before deploying**

## Advanced Usage

### Custom Domain Patterns
Modify `config.h` to add custom domain mappings:

```c
static const struct {
    const char* original;
    const char* replacement;
} DOMAIN_MAPPINGS[] = {
    {"api.binance.com", "api.binance.com.yourdomain.com"},
    {"stream.binance.com", "stream.binance.com.yourdomain.com"},
    // Add more mappings...
};
```

### Performance Optimization
Enable caching for better performance:

```c
#define CACHE_REDIRECTED_URLS 1
#define MAX_CACHE_SIZE 5000
```

This completes the basic usage workflow. The dylib will now intercept all network requests to Binance domains and redirect them to your specified domains while preserving all request data and parameters.
