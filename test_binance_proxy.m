#import <Foundation/Foundation.h>
#import "config.h"

// Test program to verify Binance-only proxy redirection logic
// Compile with: clang -framework Foundation -o test_binance_proxy test_binance_proxy.m

// Copy of the Binance domain check function for testing
BOOL isBinanceDomain(NSString* domain) {
    if (!domain) return NO;
    
    // Convert to lowercase for comparison
    NSString* lowerDomain = [domain lowercaseString];
    
    // Check for exact matches and subdomains of Binance domains
    for (int i = 0; i < BINANCE_DOMAINS_COUNT; i++) {
        NSString* binanceDomain = [[NSString stringWithUTF8String:BINANCE_DOMAINS[i]] lowercaseString];
        
        // Check if it's exactly the Binance domain or a subdomain
        if ([lowerDomain isEqualToString:binanceDomain] || 
            [lowerDomain hasSuffix:[@"." stringByAppendingString:binanceDomain]]) {
            return YES;
        }
    }
    
    return NO;
}

// Copy of the Binance proxy redirect function for testing
NSString* redirectBinanceToProxy(NSString* originalURL) {
    if (!originalURL || [originalURL length] == 0) return originalURL;

    // Check if already redirected to proxy
    if ([originalURL hasPrefix:@PROXY_SERVER]) {
        return originalURL;
    }

    NSURL* url = [NSURL URLWithString:originalURL];
    if (!url || !url.host) {
        return originalURL; // Invalid URL, return as-is
    }

    // Only redirect if it's a Binance domain
    if (!isBinanceDomain(url.host)) {
        return originalURL;
    }

    // Build proxy URL: https://proxy.x3r0x.me/original-domain/path
    NSString* proxyURL;
    NSString* path = url.path ? url.path : @"";
    NSString* query = url.query ? [@"?" stringByAppendingString:url.query] : @"";
    NSString* fragment = url.fragment ? [@"#" stringByAppendingString:url.fragment] : @"";
    
    // Remove leading slash from path if present
    if ([path hasPrefix:@"/"]) {
        path = [path substringFromIndex:1];
    }
    
    if ([path length] > 0) {
        proxyURL = [NSString stringWithFormat:@"%@/%@/%@%@%@", @PROXY_SERVER, url.host, path, query, fragment];
    } else {
        proxyURL = [NSString stringWithFormat:@"%@/%@%@%@", @PROXY_SERVER, url.host, query, fragment];
    }

    return proxyURL;
}

void testURL(NSString* original, NSString* expected) {
    NSString* result = redirectBinanceToProxy(original);
    BOOL passed = [result isEqualToString:expected];
    
    printf("%s: %s -> %s\n", 
           passed ? "✅ PASS" : "❌ FAIL",
           [original UTF8String],
           [result UTF8String]);
    
    if (!passed) {
        printf("   Expected: %s\n", [expected UTF8String]);
    }
}

int main(int argc, const char * argv[]) {
    @autoreleasepool {
        printf("🧪 BinanceRedirect Proxy URL Redirection Test\n");
        printf("============================================\n\n");
        
        printf("Configuration:\n");
        printf("  Proxy Server: %s\n", PROXY_SERVER);
        printf("  Binance Domains: %d\n\n", BINANCE_DOMAINS_COUNT);
        
        printf("Binance domains to redirect:\n");
        for (int i = 0; i < BINANCE_DOMAINS_COUNT; i++) {
            printf("  - %s\n", BINANCE_DOMAINS[i]);
        }
        printf("\n");
        
        printf("Test Cases:\n");
        printf("-----------\n");
        
        // Test Binance domains (should be redirected)
        testURL(@"https://www.binance.com/en/trade/BTC_USDT", 
                @"https://proxy.x3r0x.me/www.binance.com/en/trade/BTC_USDT");
        
        testURL(@"https://api.binance.com/api/v3/ticker/24hr", 
                @"https://proxy.x3r0x.me/api.binance.com/api/v3/ticker/24hr");
        
        testURL(@"https://native.binance.com/stream", 
                @"https://proxy.x3r0x.me/native.binance.com/stream");
        
        testURL(@"https://fapi.binance.com/fapi/v1/exchangeInfo", 
                @"https://proxy.x3r0x.me/fapi.binance.com/fapi/v1/exchangeInfo");
        
        testURL(@"https://stream.binance.com/ws/btcusdt@ticker", 
                @"https://proxy.x3r0x.me/stream.binance.com/ws/btcusdt@ticker");
        
        testURL(@"https://www.binance.info/en/markets", 
                @"https://proxy.x3r0x.me/www.binance.info/en/markets");
        
        // Test with query parameters and fragments
        testURL(@"https://api.binance.com/api/v3/klines?symbol=BTCUSDT&interval=1h&limit=100", 
                @"https://proxy.x3r0x.me/api.binance.com/api/v3/klines?symbol=BTCUSDT&interval=1h&limit=100");
        
        // Test non-Binance domains (should NOT be redirected)
        testURL(@"https://www.google.com/search?q=binance", 
                @"https://www.google.com/search?q=binance");
        
        testURL(@"https://example.com/path/file.php", 
                @"https://example.com/path/file.php");
        
        testURL(@"https://apple.com/iphone", 
                @"https://apple.com/iphone");
        
        testURL(@"https://api.coinbase.com/v2/exchange-rates", 
                @"https://api.coinbase.com/v2/exchange-rates");
        
        testURL(@"https://binance-clone.com/api", 
                @"https://binance-clone.com/api");
        
        // Test already proxied URLs (should not be double-redirected)
        testURL(@"https://proxy.x3r0x.me/api.binance.com/v3/ticker", 
                @"https://proxy.x3r0x.me/api.binance.com/v3/ticker");
        
        // Test edge cases
        testURL(@"", @"");
        testURL(nil, nil);
        
        // Test invalid URLs
        testURL(@"not-a-url", @"not-a-url");
        
        printf("\n🎯 Test completed!\n");
        printf("\nTo run this test:\n");
        printf("  clang -framework Foundation -o test_binance_proxy test_binance_proxy.m\n");
        printf("  ./test_binance_proxy\n");
        printf("\n📋 Summary:\n");
        printf("  - Only Binance domains should be redirected to proxy format\n");
        printf("  - Non-Binance domains should remain unchanged\n");
        printf("  - Already proxied URLs should not be double-redirected\n");
    }
    
    return 0;
}
