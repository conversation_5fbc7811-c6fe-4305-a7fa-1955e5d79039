#!/usr/bin/env python3
"""
BinanceRedirect Dylib Injection Helper
=====================================

This script helps inject the BinanceRedirect dylib into IPA files.
It automates the process of extracting, injecting, and repackaging IPAs.

Requirements:
- Python 3.6+
- insert_dylib or optool
- iOS App Signer or similar for re-signing

Usage:
    python inject_helper.py --ipa path/to/app.ipa --dylib libBinanceRedirect.dylib
"""

import os
import sys
import argparse
import subprocess
import tempfile
import shutil
import zipfile
from pathlib import Path

class IPAInjector:
    def __init__(self, ipa_path, dylib_path, output_path=None):
        self.ipa_path = Path(ipa_path)
        self.dylib_path = Path(dylib_path)
        self.output_path = Path(output_path) if output_path else self.ipa_path.with_suffix('.injected.ipa')
        self.temp_dir = None
        
    def __enter__(self):
        self.temp_dir = tempfile.mkdtemp()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def find_binary(self, app_dir):
        """Find the main executable binary in the app directory"""
        info_plist = app_dir / "Info.plist"
        if not info_plist.exists():
            raise Exception("Info.plist not found in app bundle")
        
        # Try to extract bundle executable name from Info.plist
        try:
            import plistlib
            with open(info_plist, 'rb') as f:
                plist = plistlib.load(f)
            executable_name = plist.get('CFBundleExecutable')
            if executable_name:
                binary_path = app_dir / executable_name
                if binary_path.exists():
                    return binary_path
        except ImportError:
            print("Warning: plistlib not available, using fallback method")
        
        # Fallback: look for executable files
        for item in app_dir.iterdir():
            if item.is_file() and os.access(item, os.X_OK):
                # Check if it's a Mach-O binary
                try:
                    result = subprocess.run(['file', str(item)], 
                                          capture_output=True, text=True)
                    if 'Mach-O' in result.stdout:
                        return item
                except:
                    pass
        
        raise Exception("Could not find main executable binary")
    
    def inject_dylib(self, binary_path, dylib_name):
        """Inject dylib into binary using insert_dylib or optool"""
        dylib_load_path = f"@executable_path/{dylib_name}"
        
        # Try insert_dylib first
        try:
            cmd = ['insert_dylib', dylib_load_path, str(binary_path)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Successfully injected using insert_dylib")
                return True
        except FileNotFoundError:
            print("insert_dylib not found, trying optool...")
        
        # Try optool
        try:
            cmd = ['optool', 'install', '-c', 'load', '-p', dylib_load_path, '-t', str(binary_path)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Successfully injected using optool")
                return True
        except FileNotFoundError:
            print("optool not found")
        
        raise Exception("Neither insert_dylib nor optool found. Please install one of them.")
    
    def inject(self):
        """Main injection process"""
        print(f"🚀 Starting injection process...")
        print(f"   IPA: {self.ipa_path}")
        print(f"   Dylib: {self.dylib_path}")
        print(f"   Output: {self.output_path}")
        
        # Verify inputs
        if not self.ipa_path.exists():
            raise Exception(f"IPA file not found: {self.ipa_path}")
        if not self.dylib_path.exists():
            raise Exception(f"Dylib file not found: {self.dylib_path}")
        
        # Extract IPA
        print("📦 Extracting IPA...")
        extract_dir = Path(self.temp_dir) / "extracted"
        with zipfile.ZipFile(self.ipa_path, 'r') as zip_ref:
            zip_ref.extractall(extract_dir)
        
        # Find app bundle
        payload_dir = extract_dir / "Payload"
        if not payload_dir.exists():
            raise Exception("Payload directory not found in IPA")
        
        app_dirs = [d for d in payload_dir.iterdir() if d.is_dir() and d.suffix == '.app']
        if not app_dirs:
            raise Exception("No .app bundle found in Payload")
        
        app_dir = app_dirs[0]
        print(f"📱 Found app bundle: {app_dir.name}")
        
        # Find main binary
        print("🔍 Finding main executable...")
        binary_path = self.find_binary(app_dir)
        print(f"   Binary: {binary_path.name}")
        
        # Copy dylib to app bundle
        print("📋 Copying dylib to app bundle...")
        dylib_dest = app_dir / self.dylib_path.name
        shutil.copy2(self.dylib_path, dylib_dest)
        
        # Inject dylib
        print("💉 Injecting dylib...")
        self.inject_dylib(binary_path, self.dylib_path.name)
        
        # Repackage IPA
        print("📦 Repackaging IPA...")
        with zipfile.ZipFile(self.output_path, 'w', zipfile.ZIP_DEFLATED) as zip_ref:
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(extract_dir)
                    zip_ref.write(file_path, arc_path)
        
        print(f"✅ Injection completed successfully!")
        print(f"   Output IPA: {self.output_path}")
        print("")
        print("⚠️  Important next steps:")
        print("1. Re-sign the IPA with your developer certificate")
        print("2. Install on device using Xcode, iOS App Installer, or similar")
        print("3. Check device console for BinanceRedirect log messages")

def main():
    parser = argparse.ArgumentParser(description='Inject BinanceRedirect dylib into IPA')
    parser.add_argument('--ipa', required=True, help='Path to input IPA file')
    parser.add_argument('--dylib', required=True, help='Path to BinanceRedirect dylib')
    parser.add_argument('--output', help='Path to output IPA (default: input.injected.ipa)')
    
    args = parser.parse_args()
    
    try:
        with IPAInjector(args.ipa, args.dylib, args.output) as injector:
            injector.inject()
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
