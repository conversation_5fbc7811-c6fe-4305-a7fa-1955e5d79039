# BinanceRedirect Dylib

A dynamic library for iOS that intercepts network requests to Binance domains and redirects them to custom domains.

## What it does

This dylib hooks into iOS networking APIs and automatically redirects Binance URLs:
- `native.binance.com` → `native.binance.com.x3r0x.me`
- `native.binance.info` → `native.binance.info.x3r0x.me`
- Any other `*.binance.*` domain → `*.binance.*.x3r0x.me`

## Features

- **Transparent redirection**: All network requests are automatically redirected
- **Preserves request data**: POST data, headers, and other request parameters remain intact
- **Comprehensive hooking**: Hooks multiple iOS networking APIs (NSURL, NSURLRequest, NSURLSession)
- **Logging**: Provides console output for debugging
- **Universal support**: Can be built for device and simulator

## Requirements

- macOS with Xcode installed
- iOS development environment
- Target iOS 12.0 or later

## Building

### Quick build for iOS device:
```bash
make device
```

### Build for iOS Simulator (testing):
```bash
make simulator
```

### Build universal binary:
```bash
make universal
```

### Check build environment:
```bash
make check
```

### Clean build artifacts:
```bash
make clean
```

## Usage

### 1. Build the dylib
```bash
make device
```

This creates `libBinanceRedirect.dylib`

### 2. Inject into IPA

You can use various tools to inject the dylib into an IPA file:

#### Using insert_dylib:
```bash
# Install insert_dylib if you haven't already
git clone https://github.com/Tyilo/insert_dylib.git
cd insert_dylib && make

# Inject the dylib
./insert_dylib @executable_path/libBinanceRedirect.dylib /path/to/app/binary
```

#### Using optool:
```bash
# Install optool
brew install optool

# Inject the dylib
optool install -c load -p "@executable_path/libBinanceRedirect.dylib" -t /path/to/app/binary
```

### 3. Repackage IPA

After injection, you'll need to:
1. Copy the dylib to the app bundle
2. Re-sign the app
3. Repackage as IPA

## Testing

You can test the dylib by:

1. Building for simulator:
```bash
make simulator
```

2. Injecting into a test app and running in iOS Simulator

3. Monitoring console output for redirection logs:
```
[BinanceRedirect] Redirected URL: https://native.binance.com/api/v1/data -> https://native.binance.com.x3r0x.me/api/v1/data
```

## How it works

The dylib uses method swizzling to hook into iOS networking APIs:

1. **NSURL creation**: Hooks `URLWithString:` to redirect URLs at creation time
2. **NSURLRequest**: Hooks `initWithURL:` to catch request creation
3. **NSURLSession**: Hooks `dataTaskWithURL:` and `dataTaskWithRequest:` for session-based requests

The redirection logic:
- Detects URLs containing `.binance.` domains
- Appends `.x3r0x.me` to the domain while preserving the full path and parameters
- Maintains all original request data (headers, POST body, etc.)

## Troubleshooting

### Build fails with SDK not found:
- Ensure Xcode is installed and up to date
- Run `xcode-select --install` if needed
- Check SDK path with `make info`

### Dylib not working after injection:
- Verify the dylib was properly injected with `otool -L /path/to/binary`
- Check that the dylib is in the app bundle
- Ensure proper code signing
- Check console logs for initialization messages

### App crashes after injection:
- Test with simulator build first
- Check for conflicting dylibs
- Verify iOS version compatibility

## Security Notes

- This dylib modifies network behavior and should only be used for legitimate testing purposes
- Ensure you have proper authorization before injecting into any application
- The redirected domains should be under your control for security

## License

This project is for educational and testing purposes only. Use responsibly and in accordance with applicable laws and terms of service.
