#import <Foundation/Foundation.h>
#import "config.h"

// Test program to verify URL redirection logic
// Compile with: clang -framework Foundation -o test_redirect test_redirect.m

// Copy of the redirect function for testing
NSString* redirectBinanceURL(NSString* originalURL) {
    if (!originalURL) return originalURL;
    
    NSString* urlString = [originalURL copy];
    BOOL wasRedirected = NO;
    
    // Check if URL contains binance domain
    if ([urlString containsString:@".binance."]) {
        // Apply specific domain mappings first
        for (int i = 0; i < DOMAIN_MAPPINGS_COUNT; i++) {
            NSString* original = [NSString stringWithUTF8String:DOMAIN_MAPPINGS[i].original];
            NSString* replacement = [NSString stringWithUTF8String:DOMAIN_MAPPINGS[i].replacement];
            
            if ([urlString containsString:original]) {
                urlString = [urlString stringByReplacingOccurrencesOfString:original 
                                                                withString:replacement];
                wasRedirected = YES;
            }
        }
        
#if ENABLE_WILDCARD_REDIRECT
        // Handle other binance subdomains with wildcard pattern
        if (!wasRedirected) {
            NSRegularExpression* regex = [NSRegularExpression regularExpressionWithPattern:BINANCE_DOMAIN_PATTERN
                                                                                   options:0
                                                                                     error:nil];
            if (regex) {
                NSString* newString = [regex stringByReplacingMatchesInString:urlString
                                                                      options:0
                                                                        range:NSMakeRange(0, [urlString length])
                                                                 withTemplate:BINANCE_REPLACEMENT_TEMPLATE];
                if (![newString isEqualToString:urlString]) {
                    urlString = newString;
                    wasRedirected = YES;
                }
            }
        }
#endif

#if VERIFY_HTTPS_REDIRECT
        // Ensure HTTPS URLs remain HTTPS
        if (wasRedirected && [originalURL hasPrefix:@"https://"] && ![urlString hasPrefix:@"https://"]) {
            urlString = [urlString stringByReplacingOccurrencesOfString:@"http://" withString:@"https://"];
        }
#endif
    }
    
    return urlString;
}

void testURL(NSString* original, NSString* expected) {
    NSString* result = redirectBinanceURL(original);
    BOOL passed = [result isEqualToString:expected];
    
    printf("%s: %s -> %s\n", 
           passed ? "✅ PASS" : "❌ FAIL",
           [original UTF8String],
           [result UTF8String]);
    
    if (!passed) {
        printf("   Expected: %s\n", [expected UTF8String]);
    }
}

int main(int argc, const char * argv[]) {
    @autoreleasepool {
        printf("🧪 BinanceRedirect URL Redirection Test\n");
        printf("=====================================\n\n");
        
        printf("Configuration:\n");
        printf("  Redirect Suffix: %s\n", REDIRECT_SUFFIX);
        printf("  Wildcard Redirect: %s\n", ENABLE_WILDCARD_REDIRECT ? "Enabled" : "Disabled");
        printf("  HTTPS Verification: %s\n", VERIFY_HTTPS_REDIRECT ? "Enabled" : "Disabled");
        printf("  Domain Mappings: %d\n\n", DOMAIN_MAPPINGS_COUNT);
        
        printf("Test Cases:\n");
        printf("-----------\n");
        
        // Test specific domain mappings
        testURL(@"https://native.binance.com/api/v1/data", 
                @"https://native.binance.com.x3r0x.me/api/v1/data");
        
        testURL(@"https://native.binance.info/stream", 
                @"https://native.binance.info.x3r0x.me/stream");
        
        testURL(@"http://api.binance.com/v3/ticker", 
                @"http://api.binance.com.x3r0x.me/v3/ticker");
        
        // Test wildcard redirection
        testURL(@"https://stream.binance.com/ws/btcusdt@ticker", 
                @"https://stream.binance.com.x3r0x.me/ws/btcusdt@ticker");
        
        testURL(@"https://fapi.binance.com/fapi/v1/exchangeInfo", 
                @"https://fapi.binance.com.x3r0x.me/fapi/v1/exchangeInfo");
        
        // Test complex URLs with parameters
        testURL(@"https://api.binance.com/api/v3/klines?symbol=BTCUSDT&interval=1h&limit=100", 
                @"https://api.binance.com.x3r0x.me/api/v3/klines?symbol=BTCUSDT&interval=1h&limit=100");
        
        // Test URLs that should NOT be redirected
        testURL(@"https://google.com/search?q=binance", 
                @"https://google.com/search?q=binance");
        
        testURL(@"https://example.com/api", 
                @"https://example.com/api");
        
        testURL(@"https://binance-clone.com/api", 
                @"https://binance-clone.com/api");
        
        // Test edge cases
        testURL(@"", @"");
        testURL(nil, nil);
        
        // Test HTTPS preservation
        testURL(@"https://new.binance.org/trading", 
                @"https://new.binance.org.x3r0x.me/trading");
        
        printf("\n🎯 Test completed!\n");
        printf("\nTo run this test:\n");
        printf("  clang -framework Foundation -o test_redirect test_redirect.m\n");
        printf("  ./test_redirect\n");
    }
    
    return 0;
}
