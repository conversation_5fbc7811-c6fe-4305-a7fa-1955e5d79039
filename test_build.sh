#!/bin/bash

# Test script for BinanceRedirect dylib build
# This script tests the build process and verifies the output

echo "🔨 BinanceRedirect Dylib Build Test"
echo "=================================="

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ Error: This script must be run on macOS"
    exit 1
fi

# Check if Xcode is installed
echo "📋 Checking build environment..."
if ! command -v xcode-select &> /dev/null; then
    echo "❌ Error: Xcode command line tools not found"
    echo "   Please run: xcode-select --install"
    exit 1
fi

# Check Xcode path
XCODE_PATH=$(xcode-select -p)
if [ ! -d "$XCODE_PATH" ]; then
    echo "❌ Error: Xcode not found at $XCODE_PATH"
    exit 1
fi

echo "✅ Xcode found at: $XCODE_PATH"

# Check for iOS SDK
IOS_SDK_PATH="$XCODE_PATH/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk"
if [ ! -d "$IOS_SDK_PATH" ]; then
    echo "❌ Error: iOS SDK not found at $IOS_SDK_PATH"
    echo "   Please ensure Xcode is properly installed with iOS SDK"
    exit 1
fi

echo "✅ iOS SDK found"

# Clean previous builds
echo ""
echo "🧹 Cleaning previous builds..."
make clean

# Test device build
echo ""
echo "📱 Testing device build (ARM64)..."
if make device; then
    echo "✅ Device build successful"
    
    # Check if dylib was created
    if [ -f "libBinanceRedirect.dylib" ]; then
        echo "✅ Dylib file created: libBinanceRedirect.dylib"
        
        # Show file info
        echo "📊 File information:"
        file libBinanceRedirect.dylib
        echo "   Size: $(du -h libBinanceRedirect.dylib | cut -f1)"
        
        # Check architecture
        echo "🏗️  Architecture:"
        lipo -info libBinanceRedirect.dylib
        
        # Check dependencies
        echo "🔗 Dependencies:"
        otool -L libBinanceRedirect.dylib
        
    else
        echo "❌ Error: Dylib file not created"
        exit 1
    fi
else
    echo "❌ Error: Device build failed"
    exit 1
fi

# Test simulator build if available
echo ""
echo "🖥️  Testing simulator build (x86_64)..."
if make simulator; then
    echo "✅ Simulator build successful"
    
    if [ -f "libBinanceRedirect_sim.dylib" ]; then
        echo "✅ Simulator dylib created: libBinanceRedirect_sim.dylib"
        echo "🏗️  Architecture:"
        lipo -info libBinanceRedirect_sim.dylib
    fi
else
    echo "⚠️  Simulator build failed (this is OK if you don't have simulator SDK)"
fi

# Test universal build
echo ""
echo "🌍 Testing universal build..."
if make universal; then
    echo "✅ Universal build successful"
    
    if [ -f "libBinanceRedirect_universal.dylib" ]; then
        echo "✅ Universal dylib created: libBinanceRedirect_universal.dylib"
        echo "🏗️  Architecture:"
        lipo -info libBinanceRedirect_universal.dylib
    fi
else
    echo "⚠️  Universal build failed (this is OK if simulator build failed)"
fi

echo ""
echo "🎉 Build test completed!"
echo ""
echo "📋 Summary:"
echo "   - Device dylib: $([ -f "libBinanceRedirect.dylib" ] && echo "✅ Created" || echo "❌ Failed")"
echo "   - Simulator dylib: $([ -f "libBinanceRedirect_sim.dylib" ] && echo "✅ Created" || echo "⚠️  Not created")"
echo "   - Universal dylib: $([ -f "libBinanceRedirect_universal.dylib" ] && echo "✅ Created" || echo "⚠️  Not created")"
echo ""
echo "🚀 Ready for IPA injection!"
echo ""
echo "Next steps:"
echo "1. Use libBinanceRedirect.dylib for device injection"
echo "2. Follow the README.md instructions for IPA injection"
echo "3. Test with your target application"
