#import <Foundation/Foundation.h>

// Test program to verify that our proxy hooks are working
// This simulates the network calls that an iOS app might make

int main(int argc, const char * argv[]) {
    @autoreleasepool {
        NSLog(@"🧪 Testing ProxyRedirect Hooks");
        NSLog(@"===============================");
        
        // Test 1: NSURL URLWithString - Various domains
        NSLog(@"\n1. Testing NSURL URLWithString:");
        NSURL *url1 = [NSURL URLWithString:@"https://api.binance.com/api/v3/ticker"];
        NSLog(@"   Binance URL: %@", [url1 absoluteString]);
        
        NSURL *url2 = [NSURL URLWithString:@"https://example.com/path/file.php"];
        NSLog(@"   Example URL: %@", [url2 absoluteString]);
        
        NSURL *url3 = [NSURL URLWithString:@"https://www.google.com/search?q=test"];
        NSLog(@"   Google URL: %@", [url3 absoluteString]);
        
        // Test 2: NSURLRequest requestWithURL
        NSLog(@"\n2. Testing NSURLRequest requestWithURL:");
        NSURLRequest *request1 = [NSURLRequest requestWithURL:url1];
        NSLog(@"   Request URL: %@", [[request1 URL] absoluteString]);
        
        // Test 3: NSURLRequest initWithURL
        NSLog(@"\n3. Testing NSURLRequest initWithURL:");
        NSURL *url4 = [NSURL URLWithString:@"https://jsonplaceholder.typicode.com/posts/1"];
        NSURLRequest *request2 = [[NSURLRequest alloc] initWithURL:url4];
        NSLog(@"   Request URL: %@", [[request2 URL] absoluteString]);
        
        // Test 4: NSMutableURLRequest
        NSLog(@"\n4. Testing NSMutableURLRequest:");
        NSURL *url5 = [NSURL URLWithString:@"https://httpbin.org/get"];
        NSMutableURLRequest *mutableRequest = [NSMutableURLRequest requestWithURL:url5];
        NSLog(@"   Mutable Request URL: %@", [[mutableRequest URL] absoluteString]);
        
        // Test 5: NSURLSession dataTask
        NSLog(@"\n5. Testing NSURLSession dataTaskWithURL:");
        NSURLSession *session = [NSURLSession sharedSession];
        NSURL *url6 = [NSURL URLWithString:@"https://api.github.com/users/octocat"];
        NSURLSessionDataTask *dataTask = [session dataTaskWithURL:url6 completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
            // This won't execute in our test
        }];
        NSLog(@"   DataTask URL: %@", [[[dataTask originalRequest] URL] absoluteString]);
        
        // Test 6: NSURLSession dataTaskWithRequest
        NSLog(@"\n6. Testing NSURLSession dataTaskWithRequest:");
        NSURL *url7 = [NSURL URLWithString:@"https://reqres.in/api/users"];
        NSURLRequest *request3 = [NSURLRequest requestWithURL:url7];
        NSURLSessionDataTask *dataTask2 = [session dataTaskWithRequest:request3 completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
            // This won't execute in our test
        }];
        NSLog(@"   DataTask Request URL: %@", [[[dataTask2 originalRequest] URL] absoluteString]);
        
        // Test 7: Excluded domains (should NOT be redirected)
        NSLog(@"\n7. Testing excluded domains:");
        NSURL *url8 = [NSURL URLWithString:@"https://apple.com/iphone"];
        NSLog(@"   Apple URL (should NOT redirect): %@", [url8 absoluteString]);
        
        NSURL *url9 = [NSURL URLWithString:@"https://localhost:8080/api"];
        NSLog(@"   Localhost URL (should NOT redirect): %@", [url9 absoluteString]);
        
        // Test 8: Already proxied URLs (should NOT be double-redirected)
        NSLog(@"\n8. Testing already proxied URLs:");
        NSURL *url10 = [NSURL URLWithString:@"https://proxy.x3r0x.me/example.com/api"];
        NSLog(@"   Already proxied URL: %@", [url10 absoluteString]);
        
        NSLog(@"\n✅ Hook testing completed!");
        NSLog(@"");
        NSLog(@"📋 What to expect:");
        NSLog(@"   - Most URLs should be redirected to https://proxy.x3r0x.me/[domain]/[path]");
        NSLog(@"   - Apple.com and localhost URLs should remain unchanged");
        NSLog(@"   - Already proxied URLs should not be double-redirected");
        NSLog(@"   - Look for [ProxyRedirect] log messages if dylib is loaded");
        NSLog(@"");
        NSLog(@"🔍 Example expected redirections:");
        NSLog(@"   https://example.com/path → https://proxy.x3r0x.me/example.com/path");
        NSLog(@"   https://api.binance.com/v3 → https://proxy.x3r0x.me/api.binance.com/v3");
        NSLog(@"   https://www.google.com/search → https://proxy.x3r0x.me/www.google.com/search");
    }
    
    return 0;
}
