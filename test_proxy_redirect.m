#import <Foundation/Foundation.h>
#import "config.h"

// Test program to verify proxy URL redirection logic
// Compile with: clang -framework Foundation -o test_proxy_redirect test_proxy_redirect.m

// Copy of the domain exclusion function for testing
BOOL shouldExcludeDomain(NSString* domain) {
    if (!domain) return YES;
    
    // Convert to lowercase for comparison
    NSString* lowerDomain = [domain lowercaseString];
    
    for (int i = 0; i < EXCLUDED_DOMAINS_COUNT; i++) {
        NSString* excludedDomain = [[NSString stringWithUTF8String:EXCLUDED_DOMAINS[i]] lowercaseString];
        if ([lowerDomain isEqualToString:excludedDomain] || [lowerDomain hasSuffix:[@"." stringByAppendingString:excludedDomain]]) {
            return YES;
        }
    }
    
    return NO;
}

// Copy of the proxy redirect function for testing
NSString* redirectToProxy(NSString* originalURL) {
    if (!originalURL || [originalURL length] == 0) return originalURL;

    // Check if already redirected to proxy
    if ([originalURL hasPrefix:@PROXY_SERVER]) {
        return originalURL;
    }

    NSURL* url = [NSURL URLWithString:originalURL];
    if (!url || !url.host) {
        return originalURL; // Invalid URL, return as-is
    }

    // Check if domain should be excluded
    if (shouldExcludeDomain(url.host)) {
        return originalURL;
    }

    // Build proxy URL: https://proxy.x3r0x.me/original-domain/path
    NSString* proxyURL;
    NSString* path = url.path ? url.path : @"";
    NSString* query = url.query ? [@"?" stringByAppendingString:url.query] : @"";
    NSString* fragment = url.fragment ? [@"#" stringByAppendingString:url.fragment] : @"";
    
    // Remove leading slash from path if present
    if ([path hasPrefix:@"/"]) {
        path = [path substringFromIndex:1];
    }
    
    if ([path length] > 0) {
        proxyURL = [NSString stringWithFormat:@"%@/%@/%@%@%@", @PROXY_SERVER, url.host, path, query, fragment];
    } else {
        proxyURL = [NSString stringWithFormat:@"%@/%@%@%@", @PROXY_SERVER, url.host, query, fragment];
    }

    return proxyURL;
}

void testURL(NSString* original, NSString* expected) {
    NSString* result = redirectToProxy(original);
    BOOL passed = [result isEqualToString:expected];
    
    printf("%s: %s -> %s\n", 
           passed ? "✅ PASS" : "❌ FAIL",
           [original UTF8String],
           [result UTF8String]);
    
    if (!passed) {
        printf("   Expected: %s\n", [expected UTF8String]);
    }
}

int main(int argc, const char * argv[]) {
    @autoreleasepool {
        printf("🧪 ProxyRedirect URL Redirection Test\n");
        printf("====================================\n\n");
        
        printf("Configuration:\n");
        printf("  Proxy Server: %s\n", PROXY_SERVER);
        printf("  Redirect All Requests: %s\n", REDIRECT_ALL_REQUESTS ? "Enabled" : "Disabled");
        printf("  Excluded Domains: %d\n\n", EXCLUDED_DOMAINS_COUNT);
        
        printf("Test Cases:\n");
        printf("-----------\n");
        
        // Test basic URL redirection
        testURL(@"https://example.com/path/file.php", 
                @"https://proxy.x3r0x.me/example.com/path/file.php");
        
        testURL(@"http://api.binance.com/v3/ticker", 
                @"https://proxy.x3r0x.me/api.binance.com/v3/ticker");
        
        testURL(@"https://www.google.com/search?q=test", 
                @"https://proxy.x3r0x.me/www.google.com/search?q=test");
        
        // Test URLs with query parameters and fragments
        testURL(@"https://example.com/api?param1=value1&param2=value2#section", 
                @"https://proxy.x3r0x.me/example.com/api?param1=value1&param2=value2#section");
        
        // Test URLs without paths
        testURL(@"https://example.com", 
                @"https://proxy.x3r0x.me/example.com");
        
        testURL(@"https://example.com/", 
                @"https://proxy.x3r0x.me/example.com/");
        
        // Test excluded domains (should NOT be redirected)
        testURL(@"https://apple.com/iphone", 
                @"https://apple.com/iphone");
        
        testURL(@"https://www.apple.com/mac", 
                @"https://www.apple.com/mac");
        
        testURL(@"https://localhost:8080/api", 
                @"https://localhost:8080/api");
        
        testURL(@"https://127.0.0.1/test", 
                @"https://127.0.0.1/test");
        
        // Test already redirected URLs (should not be double-redirected)
        testURL(@"https://proxy.x3r0x.me/example.com/api", 
                @"https://proxy.x3r0x.me/example.com/api");
        
        // Test edge cases
        testURL(@"", @"");
        testURL(nil, nil);
        
        // Test invalid URLs
        testURL(@"not-a-url", @"not-a-url");
        
        printf("\n🎯 Test completed!\n");
        printf("\nTo run this test:\n");
        printf("  clang -framework Foundation -o test_proxy_redirect test_proxy_redirect.m\n");
        printf("  ./test_proxy_redirect\n");
    }
    
    return 0;
}
